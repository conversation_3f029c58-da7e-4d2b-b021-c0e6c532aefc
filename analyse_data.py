import sys

import xlsxwriter
from colorama import Fore

from global_parameters import WORK_DIRECTORY, TODAY, YEAR_MONTH_DAY_TIME

from database import sql_connect

def run():
    conn = sql_connect()
    cursor = conn.cursor

    cursor.execute('SELECT * FROM printers ORDER BY device_id, timestamp')

    analyzing_workbook = xlsxwriter.Workbook(f'{WORK_DIRECTORY}Analyzing - {YEAR_MONTH_DAY_TIME}.xlsx')
    analyzing_worksheet = analyzing_workbook.add_worksheet('Nyomtatóállások elemzése')
    header1 = ['Device name', 'Device ID', 'IP address', 'Total - Black pages', 'Color pages', 'Scanned pages',
           'TimeStamp']
    bold = analyzing_workbook.add_format({'bold': True})

    analyzing_worksheet.write(0, 0, f'BAVKH nyomtatóállások elemzése - {YEAR_MONTH_DAY_TIME}')

    # Create xlsx table header row
    for col in range(len(header1)):
        analyzing_worksheet.write(1, col, header1[col], bold)

    rows = cursor.fetchall()

    if len(rows) == 0:
        print('No records in the table. ')
        sys.exit(0)

    xlsx_row = 1
    device_id = ''
    col = 0
    black_page, color_page, scan_page = 0, 0, 0

    for row in rows:
        if device_id != row[0]:
            xlsx_row += 1
            col = 0
            device_id = row[0]
        col += 1
        if col == 1:
            analyzing_worksheet.write(xlsx_row, 0, row[2])  # Device Name
            analyzing_worksheet.write(xlsx_row, 1, row[0])  # Device ID
            analyzing_worksheet.write(xlsx_row, 2, row[3])  # IP
            analyzing_worksheet.write(xlsx_row, 3 + (col - 1) * 4, row[6])  # Black pages
            analyzing_worksheet.write(xlsx_row, 4 + (col - 1) * 4, row[7])  # Color pages
            analyzing_worksheet.write(xlsx_row, 5 + (col - 1) * 4, row[8])  # Scanned pages
            analyzing_worksheet.write(xlsx_row, 6 + (col - 1) * 4, row[1])  # TimeStamp
            black_page = row[6]
            color_page = row[7]
            scan_page = row[8]
        else:
            analyzing_worksheet.write(xlsx_row, 3 + (col - 1) * 4, row[6] - black_page)  # Black pages
            analyzing_worksheet.write(xlsx_row, 4 + (col - 1) * 4, row[7] - color_page)  # Color pages
            analyzing_worksheet.write(xlsx_row, 5 + (col - 1) * 4, row[8] - scan_page)  # Scanned pages
            analyzing_worksheet.write(xlsx_row, 6 + (col - 1) * 4, row[1])  # TimeStamp

        if black_page > row[6] or color_page > row[7] or scan_page > row[8]:
            print(f'{Fore.CYAN}{row}')

    analyzing_worksheet.autofit()
    analyzing_workbook.close()
