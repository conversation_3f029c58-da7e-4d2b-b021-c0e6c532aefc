# 🖨️ SNMP Nyom<PERSON>r - Projekt Struktúra

## 📁 Fő Fájlok

### 🚀 **Indító F<PERSON>**
- **`start_gui.py`** - 🎯 **AJÁNLOTT INDÍTÁS** - GUI starter menüvel
- **`launcher.py`** - Grafikus launcher (verzió választó)
- **`snmp.py`** - Eredeti konzol verzió

### 🖥️ **GUI Verziók**
- **`snmp_gui_advanced.py`** - Fejlett GUI (többfüles, teljes funkciók)
- **`snmp_gui_simple.py`** - Egyszerű GUI (könnyen használható)
- **`snmp_gui_minimal.py`** - Minimális GUI (alapvető funkciók)

### ⚙️ **Alap Modulok**
- **`snmp_getter.py`** - SNMP kommunikációs logika
- **`global_parameters.py`** - Konfigurációk és OID-ok
- **`new_ip_import.py`** - IP lista beolvasás
- **`save_printer_oid_answers.py`** - Eredmények mentése
- **`email_sender.py`** - Email küldés
- **`analyse_data.py`** - Adatelemzés
- **`database.py`** - Adatbázis műveletek
- **`save_sql_command.py`** - SQL parancsok

### 📄 **Konfigurációs Fájlok**
- **`snmp_config.json`** - GUI beállítások
- **`run_gui.bat`** - Windows batch indító

### 📚 **Dokumentáció**
- **`QUICK_START.md`** - Gyors indítási útmutató
- **`GUI_README.md`** - Részletes GUI dokumentáció
- **`PERFORMANCE_FIX.md`** - Teljesítmény javítások
- **`PROJECT_STRUCTURE.md`** - Ez a fájl

### 📂 **Könyvtárak**
- **`work/`** - Eredmények, IP listák, Excel fájlok
- **`__pycache__/`** - Python cache fájlok

## 🎯 Használat

### **Ajánlott Indítás:**
```bash
py start_gui.py
```

**Menü opciók:**
- `1` - Minimális GUI (kezdőknek)
- `2` - Egyszerű GUI (több funkcióval)
- `3` - Fejlett GUI (teljes funkcionalitás)
- `4` - Launcher (verzió választó)
- `5` - Konzol verzió
- `6` - Tkinter teszt

### **Közvetlen Indítás:**
```bash
# GUI verziók
py snmp_gui_advanced.py    # Fejlett (ajánlott)
py snmp_gui_simple.py      # Egyszerű
py snmp_gui_minimal.py     # Minimális

# Konzol verzió
py snmp.py run

# Launcher
py launcher.py
```

## 📊 Verzió Összehasonlítás

| Funkció | Minimális | Egyszerű | Fejlett | Konzol |
|---------|-----------|----------|---------|--------|
| SNMP szkennelés | ✅ | ✅ | ✅ | ✅ |
| Párhuzamos futás | ✅ | ✅ | ✅ | ✅ |
| Valós idejű statisztikák | ✅ | ✅ | ✅ | ❌ |
| Színes log | ✅ | ✅ | ✅ | ❌ |
| Beállítások mentése | ❌ | ❌ | ✅ | ❌ |
| Teszt funkciók | ✅ | ❌ | ✅ | ❌ |
| Többfüles felület | ❌ | ❌ | ✅ | ❌ |
| Exportálás | ❌ | ❌ | ✅ | ✅ |
| Tanulási görbe | Könnyű | Könnyű | Közepes | Nehéz |
| Sebesség | ⚡ Gyors | ⚡ Gyors | ⚡ Gyors | ⚡ Gyors |

## 🔧 Rendszerkövetelmények

- **Python:** 3.7+
- **Csomagok:** pysnmp, tkinter
- **OS:** Windows/Linux/Mac
- **RAM:** 4GB+ (fejlett GUI-hoz)
- **Hálózat:** SNMP hozzáférés a nyomtatókhoz

## 📁 Work Könyvtár

```
work/
├── ip.txt                           # IP lista (TAB elválasztott)
├── answers-YYYY.MM.DD-HH.MM-*.txt   # Sikeres válaszok
├── bad_answers-YYYY.MM.DD-HH.MM-*.txt # Hibás válaszok
├── Nyomtatóállások *.xlsx           # Excel jelentések
└── snmp_app.log                     # Log fájl
```

## 🚀 Teljesítmény

**Minden GUI verzió most párhuzamosan fut:**
- **394 eszköz:** ~10-20 másodperc
- **Sebesség:** ~100-500 eszköz/perc
- **Egyidejű kapcsolatok:** 10-300 (beállítható)

## 🎉 Főbb Funkciók

### ✅ **Minden verzióban:**
- Párhuzamos SNMP szkennelés
- Tcl/Tk automatikus javítás
- Informatikus szűrés
- Eredmények automatikus mentése
- Work mappa gyors megnyitása

### ✅ **GUI verziókban:**
- Valós idejű statisztikák
- Színes log üzenetek
- Progress bar ETA-val
- Start/Stop vezérlés
- Sebesség mérés

### ✅ **Fejlett GUI-ban:**
- Többfüles felület
- Részletes beállítások
- Ping/SNMP tesztek
- Konfigurációs fájl
- Exportálási opciók

---

**Projekt verzió:** 2.0  
**Utolsó frissítés:** 2025-08-08  
**Állapot:** ✅ Teljes mértékben működőképes
