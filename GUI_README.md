# 🖨️ SNMP Nyomtató Scanner - Grafikus Felületek

## Áttekintés

Az SNMP Nyomtató Scanner most már három kü<PERSON>önböző grafikus felülettel rendelkezik, am<PERSON><PERSON> kü<PERSON>ö<PERSON><PERSON><PERSON><PERSON><PERSON> felhasználói igényeket elégítenek ki.

## 🚀 Indítás

### Launcher has<PERSON> (Ajánlott)
```bash
python launcher.py
```

### Közvetlen indítás
```bash
# Egyszerű verzió
python snmp_gui_simple.py

# Fejlett verzió  
python snmp_gui_advanced.py

# Konzol verzió
python snmp.py run
```

## 📋 Verziók

### 🚀 Egyszerű Verzió (`snmp_gui_simple.py`)

**Célcsoport:** <PERSON>zd<PERSON> fel<PERSON>z<PERSON>lók, gyors szkennel<PERSON>

**Funkciók:**
- ✅ E<PERSON>ű, intuitív kezelőfelület
- ✅ Valós idejű statisztikák (összes, sikeres, sikertelen)
- ✅ Sebesség és ETA számítás
- ✅ Színes log üzenetek (INFO, SUCCESS, ERROR, WARNING)
- ✅ Automatikus görgetés opció
- ✅ Log mentése és törlése
- ✅ Work mappa gyors megnyitása
- ✅ Informatikus szűrő
- ✅ Debug, Email, Excel opciók

**Előnyök:**
- Könnyen tanulható
- Gyors használat
- Minimális beállítások
- Stabil működés

### ⚡ Fejlett Verzió (`snmp_gui_advanced.py`)

**Célcsoport:** Haladó felhasználók, rendszergazdák

**Funkciók:**
- ✅ Többfüles felület (Szkennelés, Beállítások, Eredmények)
- ✅ Részletes SNMP beállítások (timeout, community string)
- ✅ Ping és SNMP teszt funkciók
- ✅ Eszköz lista szűrése és rendezése
- ✅ Exportálási lehetőségek (Excel, CSV)
- ✅ Konfigurációs fájl mentése/betöltése
- ✅ Kontextus menü
- ✅ Fejlett hibakezelés
- ✅ Menüsor teljes funkcionalitással

**Előnyök:**
- Teljes funkcionalitás
- Testreszabható beállítások
- Professzionális megjelenés
- Exportálási lehetőségek

### 💻 Konzol Verzió (`snmp.py`)

**Célcsoport:** Tapasztalt felhasználók, automatizálás

**Funkciók:**
- ✅ Parancssori futtatás
- ✅ Minimális erőforrásigény
- ✅ Batch feldolgozás
- ✅ Scriptelhető
- ✅ Gyors végrehajtás

**Előnyök:**
- Leggyorsabb végrehajtás
- Automatizálható
- Távoli futtatás
- Minimális GUI overhead

## 🎛️ Launcher (`launcher.py`)

A launcher egy központi indítófelület, amely lehetővé teszi:

- 🎯 Verzió kiválasztása egyszerű gombokkal
- 📝 Verzió leírások és funkciók áttekintése
- 📂 Work mappa gyors megnyitása
- ℹ️ Beépített súgó
- 🎨 Modern, felhasználóbarát design

## 📊 Funkciók Összehasonlítása

| Funkció | Egyszerű | Fejlett | Konzol |
|---------|----------|---------|--------|
| SNMP szkennelés | ✅ | ✅ | ✅ |
| Valós idejű statisztikák | ✅ | ✅ | ❌ |
| Színes log | ✅ | ✅ | ❌ |
| Beállítások mentése | ❌ | ✅ | ❌ |
| Teszt funkciók | ❌ | ✅ | ❌ |
| Exportálás | ❌ | ✅ | ✅ |
| Többfüles felület | ❌ | ✅ | ❌ |
| Erőforrásigény | Közepes | Nagy | Kicsi |
| Tanulási görbe | Könnyű | Közepes | Nehéz |

## 🔧 Telepítés és Követelmények

### Python csomagok
```bash
pip install pysnmp
pip install tkinter  # Általában beépített
```

### Rendszerkövetelmények
- Python 3.7 vagy újabb
- Windows/Linux/Mac OS
- Minimum 4GB RAM (fejlett verzióhoz)
- Hálózati kapcsolat az SNMP eszközökhöz

## 📁 Fájlstruktúra

```
SNMP_Py/
├── launcher.py              # Központi indító
├── snmp_gui_simple.py       # Egyszerű GUI
├── snmp_gui_advanced.py     # Fejlett GUI
├── snmp.py                  # Konzol verzió
├── snmp_getter.py           # SNMP logika
├── global_parameters.py     # Konfigurációk
├── work/                    # Eredmények
│   ├── ip.txt              # IP lista
│   ├── answers-*.txt       # Válaszok
│   └── bad_answers-*.txt   # Hibák
└── GUI_README.md           # Ez a fájl
```

## 🎯 Használati Útmutató

### 1. Első indítás
1. Futtassa a `launcher.py`-t
2. Válassza ki a kívánt verziót
3. Ellenőrizze az IP lista fájlt (`work/ip.txt`)

### 2. Szkennelés
1. Válassza ki az informatikus szűrőt
2. Állítsa be az opciókat (Debug, Email, Excel)
3. Kattintson a "Szkennelés Indítása" gombra
4. Kövesse nyomon a progresst

### 3. Eredmények
- **Válaszok:** `work/answers-YYYY.MM.DD-HH.MM-Informatikus.txt`
- **Hibák:** `work/bad_answers-YYYY.MM.DD-HH.MM-Informatikus.txt`
- **Excel:** Automatikusan generálódik (ha be van kapcsolva)
- **Email:** Automatikusan küldődik (ha be van kapcsolva)

## 🐛 Hibaelhárítás

### Gyakori problémák

**1. "IP lista fájl nem található"**
- Ellenőrizze, hogy létezik-e a `work/ip.txt` fájl
- Győződjön meg róla, hogy a fájl formátuma helyes (TAB elválasztott)

**2. "SNMP timeout hibák"**
- Ellenőrizze a hálózati kapcsolatot
- Növelje a timeout értéket (fejlett verzióban)
- Ellenőrizze, hogy az SNMP engedélyezett az eszközökön

**3. "GUI nem indul el"**
- Ellenőrizze a Python és tkinter telepítését
- Futtassa konzolból a részletes hibaüzenetekért

**4. "Lassú szkennelés"**
- Csökkentse az egyidejű kapcsolatok számát
- Ellenőrizze a hálózat sebességét
- Kapcsolja ki a debug módot

### Debug mód

A debug mód bekapcsolásával részletes SNMP kommunikációs információkat kaphat:

```python
# Egyszerű verzióban: Debug checkbox
# Fejlett verzióban: Beállítások fül
# Konzol verzióban: snmp_getter.py-ban uncomment
```

## 📞 Támogatás

- **Dokumentáció:** README.md fájlok
- **Hibák jelentése:** GitHub Issues
- **Fejlesztői dokumentáció:** Kód kommentek

## 🔄 Frissítések

### v2.0 Újdonságok
- ✨ Három különböző GUI verzió
- 🚀 Launcher alkalmazás
- 📊 Valós idejű statisztikák
- 🎨 Modern design
- 🔧 Fejlett beállítások
- 📈 Teljesítmény optimalizálás

### Tervezett funkciók
- 📱 Mobil-barát felület
- 🌐 Web-alapú dashboard
- 📊 Grafikus jelentések
- 🔔 Push értesítések
- 🗄️ Adatbázis integráció

---

**Készítette:** AI Assistant  
**Verzió:** 2.0  
**Utolsó frissítés:** 2025-08-08
