from global_parameters import WORK_DIRECTORY, YEAR_MONTH_DAY_TIME, SCANNED_PRINTER
import os, logging

logging.basicConfig(filename=WORK_DIRECTORY + 'snmp_app.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


class SavePrinterOidAnswers:
    def __init__(self, answers, bad_answers, it_specialist):
        self.answers = answers
        self.bad_answers = bad_answers
        self.it_specialist = it_specialist

    @staticmethod
    def create_report(file_name, data_list, separator):
        try:
            with open(os.path.join(WORK_DIRECTORY, file_name), 'w', encoding='utf-8') as file:
                lines = [separator.join(map(str, data)) + '\n' for data in data_list]
                file.writelines(lines)
            logging.info(f'File {file_name} created successfully.')
        except Exception as e:
            logging.error(f'Failed to create file {file_name}: {e}')

    @staticmethod
    def append_scanned_printer(my_list):
        try:
            with open(f'{WORK_DIRECTORY}{SCANNED_PRINTER}', 'a', encoding='utf-8') as file:
                lines = set([''.join(map(str, answer[2])) + '\n' for answer in my_list])
                file.writelines(lines)
            logging.info(f'File {SCANNED_PRINTER} created successfully.')
        except Exception as e:
            print(f'A {SCANNED_PRINTER} állomány elkészítése nem sikerült! {e}')
            logging.error(f'Failed to create file {SCANNED_PRINTER}: {e}')

    def save_answers(self):
        self.create_report(f'answers-{YEAR_MONTH_DAY_TIME}-{self.it_specialist}.txt', self.answers, '\t')
        self.create_report(f'bad_answers-{YEAR_MONTH_DAY_TIME}-{self.it_specialist}.txt', self.bad_answers, '\t')
        self.append_scanned_printer(self.answers)
