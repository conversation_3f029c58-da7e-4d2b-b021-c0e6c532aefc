import os
import sys

# Tcl/Tk környezeti változók beállít<PERSON>a (Python 3.13 + venv fix)
def setup_tcl_tk():
    # Lehetséges Python könyvtárak (venv és fő telepítés)
    python_dirs = []

    # Jelenlegi Python könyvtár
    python_dirs.append(os.path.dirname(sys.executable))

    # Ha virtuális környezetben vagyunk, keressük a fő Python telepítést
    if hasattr(sys, 'base_executable'):
        python_dirs.append(os.path.dirname(sys.base_executable))

    # Hardcoded útvonal a fő Python telepítéshez
    python_dirs.append("C:/Users/<USER>/AppData/Local/Programs/Python/Python313")

    for python_dir in python_dirs:
        tcl_path = os.path.join(python_dir, "tcl", "tcl8.6")
        tk_path = os.path.join(python_dir, "tcl", "tk8.6")

        if os.path.exists(tcl_path) and os.path.exists(tk_path):
            os.environ['TCL_LIBRARY'] = tcl_path
            os.environ['TK_LIBRARY'] = tk_path
            return True
    return False

# Tcl/Tk beállítása az import előtt
setup_tcl_tk()

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import asyncio
import json
from datetime import datetime
import queue
import webbrowser

from snmp_getter import SnmpGetter
from global_parameters import WORK_DIRECTORY, IP_LIST_FILE_NAME
import save_printer_oid_answers
from email_sender import send_email


class EmailDialog:
    """Email paraméterek bekérése dialógus"""
    def __init__(self, parent):
        self.result = False
        self.receiver_name = ""
        self.receiver_email = ""

        # Dialógus ablak
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Email küldés")
        self.dialog.geometry("400x250")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Középre helyezés
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (250 // 2)
        self.dialog.geometry(f"400x250+{x}+{y}")

        self.setup_ui()

        # Várakozás a dialógus bezárására
        self.dialog.wait_window()

    def setup_ui(self):
        # Cím
        title_label = tk.Label(self.dialog, text="📧 Email Küldés",
                              font=('Arial', 14, 'bold'))
        title_label.pack(pady=10)

        # Fő frame
        main_frame = tk.Frame(self.dialog)
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Címzett neve
        tk.Label(main_frame, text="Címzett neve:", font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.name_var = tk.StringVar(value="Rendszergazda")
        name_entry = tk.Entry(main_frame, textvariable=self.name_var, font=('Arial', 10), width=40)
        name_entry.pack(fill='x', pady=(0, 10))

        # Email cím
        tk.Label(main_frame, text="Email cím:", font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.email_var = tk.StringVar(value="<EMAIL>")
        email_entry = tk.Entry(main_frame, textvariable=self.email_var, font=('Arial', 10), width=40)
        email_entry.pack(fill='x', pady=(0, 10))

        # Információ
        info_text = ("Az email tartalmazza:\n"
                    "• SNMP szkennelés eredményeit\n"
                    "• Sikeres és sikertelen eszközök listáját\n"
                    "• Összefoglaló statisztikákat")

        info_label = tk.Label(main_frame, text=info_text, font=('Arial', 9),
                             fg='#666666', justify='left')
        info_label.pack(pady=10)

        # Gombok
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill='x', pady=10)

        tk.Button(button_frame, text="📧 Küldés", command=self.send_clicked,
                 bg='#27ae60', fg='white', font=('Arial', 10, 'bold'), width=12).pack(side='left', padx=5)

        tk.Button(button_frame, text="❌ Mégse", command=self.cancel_clicked,
                 bg='#e74c3c', fg='white', font=('Arial', 10, 'bold'), width=12).pack(side='right', padx=5)

        # Enter és Escape kezelése
        self.dialog.bind('<Return>', lambda e: self.send_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())

        # Fókusz az első mezőre
        name_entry.focus_set()
        name_entry.select_range(0, tk.END)

    def send_clicked(self):
        name = self.name_var.get().strip()
        email = self.email_var.get().strip()

        if not name:
            messagebox.showerror("Hiba", "Adja meg a címzett nevét!")
            return

        if not email or '@' not in email:
            messagebox.showerror("Hiba", "Adjon meg egy érvényes email címet!")
            return

        self.receiver_name = name
        self.receiver_email = email
        self.result = True
        self.dialog.destroy()

    def cancel_clicked(self):
        self.result = False
        self.dialog.destroy()


class AdvancedSnmpGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🖨️ SNMP Nyomtató Scanner Pro")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2c3e50')
        
        # Változók
        self.is_scanning = False
        self.snmp_getter = None
        self.scan_thread = None
        self.message_queue = queue.Queue()
        self.config_file = "snmp_config.json"
        self.all_results = []  # Eredmények tárolása szűréshez
        
        # Konfiguráció betöltése
        self.load_config()
        
        # Stílus beállítása
        self.setup_style()
        
        # UI létrehozása
        self.setup_ui()
        self.check_queue()
        
    def setup_style(self):
        """Modern stílus beállítása"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Színek
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground='white', background='#2c3e50')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), foreground='#34495e')
        style.configure('Success.TLabel', font=('Arial', 10, 'bold'), foreground='#27ae60')
        style.configure('Error.TLabel', font=('Arial', 10, 'bold'), foreground='#e74c3c')
        
    def load_config(self):
        """Konfiguráció betöltése"""
        self.config = {
            "ip_file": f"{WORK_DIRECTORY}{IP_LIST_FILE_NAME}",
            "specialist": "Informatikus",
            "debug": False,
            "email": True,
            "excel": True,
            "timeout": 5.0,
            "max_concurrent": 10
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
        except Exception as e:
            print(f"Konfiguráció betöltési hiba: {e}")
    
    def save_config(self):
        """Konfiguráció mentése"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Konfiguráció mentési hiba: {e}")
    
    def setup_ui(self):
        # Menüsor
        self.create_menu()
        
        # Főcím
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        title_label = ttk.Label(header_frame, text="🖨️ SNMP Nyomtató Scanner Pro", 
                               style='Title.TLabel')
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(header_frame, text="Professzionális nyomtató monitoring és adatgyűjtés", 
                                 font=('Arial', 10), fg='#bdc3c7', bg='#2c3e50')
        subtitle_label.pack()
        
        # Notebook (fülek)
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Szkennelés fül
        self.scan_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.scan_frame, text="🔍 Szkennelés")
        self.setup_scan_tab()
        
        # Beállítások fül
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text="⚙️ Beállítások")
        self.setup_settings_tab()
        
        # Eredmények fül
        self.results_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.results_frame, text="📊 Eredmények")
        self.setup_results_tab()

        # IP kezelő fül
        self.ip_manager_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.ip_manager_frame, text="📝 IP Kezelő")
        self.setup_ip_manager_tab()
        
        # Státuszsor
        self.setup_status_bar()
    
    def create_menu(self):
        """Menüsor létrehozása"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Fájl menü
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Fájl", menu=file_menu)
        file_menu.add_command(label="IP lista megnyitása", command=self.browse_ip_file)
        file_menu.add_command(label="Work mappa megnyitása", command=self.open_work_folder)
        file_menu.add_separator()
        file_menu.add_command(label="Konfiguráció mentése", command=self.save_config)
        file_menu.add_separator()
        file_menu.add_command(label="Kilépés", command=self.root.quit)
        
        # Eszközök menü
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Eszközök", menu=tools_menu)
        tools_menu.add_command(label="IP ping teszt", command=self.ping_test)
        tools_menu.add_command(label="SNMP teszt", command=self.snmp_test)
        tools_menu.add_separator()
        tools_menu.add_command(label="IP lista frissítése", command=self.refresh_ip_list)
        tools_menu.add_separator()
        tools_menu.add_command(label="Adatbázis törlése (erase)", command=self.erase_database)
        tools_menu.add_command(label="Teljes törlés (release)", command=self.release_all_data)
        tools_menu.add_separator()
        tools_menu.add_command(label="Adatelemzés (analyse)", command=self.analyse_data)
        tools_menu.add_command(label="SQL export (sql)", command=self.export_sql)
        tools_menu.add_separator()
        tools_menu.add_command(label="Log fájlok törlése", command=self.clear_logs)
        
        # Súgó menü
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Súgó", menu=help_menu)
        help_menu.add_command(label="Dokumentáció", command=self.show_help)
        help_menu.add_command(label="Névjegy", command=self.show_about)
    
    def setup_scan_tab(self):
        """Szkennelés fül beállítása"""
        # Bal oldali vezérlőpanel
        control_frame = ttk.LabelFrame(self.scan_frame, text="🎛️ Vezérlés", padding=10)
        control_frame.pack(side='left', fill='y', padx=(0, 5))
        
        # Gyors beállítások
        quick_frame = ttk.LabelFrame(control_frame, text="Gyors beállítások", padding=5)
        quick_frame.pack(fill='x', pady=(0, 10))
        
        self.specialist_var = tk.StringVar(value=self.config["specialist"])
        ttk.Label(quick_frame, text="Informatikus:").pack(anchor='w')
        specialist_combo = ttk.Combobox(quick_frame, textvariable=self.specialist_var, 
                                       values=["Informatikus", "Minden"], width=20)
        specialist_combo.pack(fill='x', pady=2)
        
        # Szkennelés opciók
        options_frame = ttk.LabelFrame(control_frame, text="Opciók", padding=5)
        options_frame.pack(fill='x', pady=(0, 10))
        
        self.debug_var = tk.BooleanVar(value=self.config["debug"])
        ttk.Checkbutton(options_frame, text="Debug üzenetek", variable=self.debug_var).pack(anchor='w')
        
        self.email_var = tk.BooleanVar(value=self.config["email"])
        ttk.Checkbutton(options_frame, text="Email küldése", variable=self.email_var).pack(anchor='w')
        
        self.excel_var = tk.BooleanVar(value=self.config["excel"])
        ttk.Checkbutton(options_frame, text="Excel generálása", variable=self.excel_var).pack(anchor='w')
        
        # Vezérlő gombok
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill='x', pady=10)
        
        self.scan_button = tk.Button(button_frame, text="🚀 Szkennelés indítása", 
                                    command=self.start_scan, bg='#27ae60', fg='white',
                                    font=('Arial', 11, 'bold'), height=2)
        self.scan_button.pack(fill='x', pady=2)
        
        self.stop_button = tk.Button(button_frame, text="⏹️ Leállítás", 
                                    command=self.stop_scan, bg='#e74c3c', fg='white',
                                    font=('Arial', 11, 'bold'), height=2, state='disabled')
        self.stop_button.pack(fill='x', pady=2)
        
        self.pause_button = tk.Button(button_frame, text="⏸️ Szünet", 
                                     command=self.pause_scan, bg='#f39c12', fg='white',
                                     font=('Arial', 10), state='disabled')
        self.pause_button.pack(fill='x', pady=2)
        
        # Jobb oldali eredmény panel
        result_frame = ttk.Frame(self.scan_frame)
        result_frame.pack(side='right', fill='both', expand=True)
        
        # Statisztikák
        stats_frame = ttk.LabelFrame(result_frame, text="📈 Statisztikák", padding=5)
        stats_frame.pack(fill='x', pady=(0, 5))
        
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill='x')
        
        # Statisztika címkék rácsos elrendezésben
        ttk.Label(stats_grid, text="Összes:").grid(row=0, column=0, sticky='w', padx=5)
        self.total_label = ttk.Label(stats_grid, text="0", font=('Arial', 10, 'bold'))
        self.total_label.grid(row=0, column=1, sticky='w', padx=5)
        
        ttk.Label(stats_grid, text="Sikeres:").grid(row=0, column=2, sticky='w', padx=5)
        self.success_label = ttk.Label(stats_grid, text="0", style='Success.TLabel')
        self.success_label.grid(row=0, column=3, sticky='w', padx=5)
        
        ttk.Label(stats_grid, text="Sikertelen:").grid(row=0, column=4, sticky='w', padx=5)
        self.failed_label = ttk.Label(stats_grid, text="0", style='Error.TLabel')
        self.failed_label.grid(row=0, column=5, sticky='w', padx=5)
        
        ttk.Label(stats_grid, text="Sebesség:").grid(row=1, column=0, sticky='w', padx=5)
        self.speed_label = ttk.Label(stats_grid, text="0 eszköz/perc")
        self.speed_label.grid(row=1, column=1, columnspan=2, sticky='w', padx=5)
        
        ttk.Label(stats_grid, text="Becsült idő:").grid(row=1, column=3, sticky='w', padx=5)
        self.eta_label = ttk.Label(stats_grid, text="--:--")
        self.eta_label.grid(row=1, column=4, columnspan=2, sticky='w', padx=5)
        
        # Progressbar fejlett verzió
        progress_frame = ttk.Frame(result_frame)
        progress_frame.pack(fill='x', pady=5)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                           maximum=100, length=400, mode='determinate')
        self.progress_bar.pack(fill='x', padx=5)
        
        self.progress_label = ttk.Label(progress_frame, text="Kész a szkennelésre")
        self.progress_label.pack(pady=2)
        
        # Log terület fejlett verzió
        log_frame = ttk.LabelFrame(result_frame, text="📝 Részletes log", padding=5)
        log_frame.pack(fill='both', expand=True)
        
        # Log vezérlők
        log_controls = ttk.Frame(log_frame)
        log_controls.pack(fill='x', pady=(0, 5))
        
        ttk.Button(log_controls, text="🗑️ Törlés", command=self.clear_log).pack(side='left', padx=2)
        ttk.Button(log_controls, text="💾 Mentés", command=self.save_log).pack(side='left', padx=2)
        
        # Log szűrők
        ttk.Label(log_controls, text="Szűrő:").pack(side='left', padx=(10, 2))
        self.log_filter_var = tk.StringVar(value="Minden")
        log_filter = ttk.Combobox(log_controls, textvariable=self.log_filter_var, 
                                 values=["Minden", "INFO", "SUCCESS", "ERROR", "WARNING"], width=10)
        log_filter.pack(side='left', padx=2)
        log_filter.bind('<<ComboboxSelected>>', self.filter_log)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=70,
                                                 font=('Consolas', 9), wrap=tk.WORD)
        self.log_text.pack(fill='both', expand=True)

    def setup_settings_tab(self):
        """Beállítások fül"""
        # Fájl beállítások
        file_frame = ttk.LabelFrame(self.settings_frame, text="📁 Fájl beállítások", padding=10)
        file_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(file_frame, text="IP lista fájl:").pack(anchor='w')
        ip_frame = ttk.Frame(file_frame)
        ip_frame.pack(fill='x', pady=2)

        self.ip_file_var = tk.StringVar(value=self.config["ip_file"])
        ttk.Entry(ip_frame, textvariable=self.ip_file_var, width=50).pack(side='left', fill='x', expand=True)
        ttk.Button(ip_frame, text="📁 Tallózás", command=self.browse_ip_file).pack(side='right', padx=(5, 0))

        # SNMP beállítások
        snmp_frame = ttk.LabelFrame(self.settings_frame, text="🌐 SNMP beállítások", padding=10)
        snmp_frame.pack(fill='x', padx=10, pady=5)

        settings_grid = ttk.Frame(snmp_frame)
        settings_grid.pack(fill='x')

        ttk.Label(settings_grid, text="Timeout (másodperc):").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.timeout_var = tk.DoubleVar(value=self.config["timeout"])
        ttk.Spinbox(settings_grid, from_=1.0, to=30.0, increment=0.5, textvariable=self.timeout_var, width=10).grid(row=0, column=1, sticky='w', padx=5, pady=2)

        ttk.Label(settings_grid, text="Max egyidejű kapcsolat:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.concurrent_var = tk.IntVar(value=self.config["max_concurrent"])
        ttk.Spinbox(settings_grid, from_=1, to=50, textvariable=self.concurrent_var, width=10).grid(row=1, column=1, sticky='w', padx=5, pady=2)

        ttk.Label(settings_grid, text="Community string:").grid(row=2, column=0, sticky='w', padx=5, pady=2)
        self.community_var = tk.StringVar(value="public")
        ttk.Entry(settings_grid, textvariable=self.community_var, width=15).grid(row=2, column=1, sticky='w', padx=5, pady=2)

        # Mentés gomb
        ttk.Button(snmp_frame, text="💾 Beállítások mentése", command=self.save_settings).pack(pady=10)

        # Teszt eszközök
        test_frame = ttk.LabelFrame(self.settings_frame, text="🧪 Teszt eszközök", padding=10)
        test_frame.pack(fill='x', padx=10, pady=5)

        # Eszköz választó
        device_select_frame = ttk.Frame(test_frame)
        device_select_frame.pack(fill='x', pady=2)

        ttk.Label(device_select_frame, text="Eszköz választás:").pack(side='left')
        self.test_device_var = tk.StringVar()
        self.test_device_combo = ttk.Combobox(device_select_frame, textvariable=self.test_device_var,
                                             width=35, state='readonly')
        self.test_device_combo.pack(side='left', padx=5)
        ttk.Button(device_select_frame, text="🔄", command=self.refresh_device_list).pack(side='left', padx=2)

        # Teszt gombok
        test_buttons_frame = ttk.Frame(test_frame)
        test_buttons_frame.pack(fill='x', pady=2)

        ttk.Button(test_buttons_frame, text="🏓 Ping Teszt", command=self.ping_test).pack(side='left', padx=2)
        ttk.Button(test_buttons_frame, text="🔍 SNMP Teszt", command=self.snmp_test).pack(side='left', padx=2)

        # Manuális IP teszt
        manual_ip_frame = ttk.Frame(test_frame)
        manual_ip_frame.pack(fill='x', pady=2)

        ttk.Label(manual_ip_frame, text="Manuális IP:").pack(side='left')
        self.test_ip_var = tk.StringVar(value="*************")
        ttk.Entry(manual_ip_frame, textvariable=self.test_ip_var, width=15).pack(side='left', padx=5)
        ttk.Button(manual_ip_frame, text="🏓", command=self.ping_manual).pack(side='left', padx=2)
        ttk.Button(manual_ip_frame, text="🔍", command=self.snmp_manual).pack(side='left', padx=2)

        # Eszköz lista betöltése (késleltetett)
        self.root.after(100, self.refresh_device_list)

    def setup_results_tab(self):
        """Eredmények fül"""
        # Eszköz lista
        devices_frame = ttk.LabelFrame(self.results_frame, text="🖨️ Eszközök", padding=5)
        devices_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Szűrők
        filter_frame = ttk.Frame(devices_frame)
        filter_frame.pack(fill='x', pady=(0, 5))

        ttk.Label(filter_frame, text="Szűrés:").pack(side='left', padx=2)
        self.device_filter_var = tk.StringVar()
        ttk.Entry(filter_frame, textvariable=self.device_filter_var, width=20).pack(side='left', padx=2)
        ttk.Button(filter_frame, text="🔍", command=self.filter_devices).pack(side='left', padx=2)
        ttk.Button(filter_frame, text="🗑️", command=self.clear_device_filter).pack(side='left', padx=2)

        ttk.Label(filter_frame, text="Státusz:").pack(side='left', padx=(10, 2))
        self.status_filter_var = tk.StringVar(value="Minden")
        status_combo = ttk.Combobox(filter_frame, textvariable=self.status_filter_var,
                                   values=["Minden", "Sikeres", "Hiba", "Timeout"], width=10, state='readonly')
        status_combo.pack(side='left', padx=2)
        status_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_devices())

        # Eredmény táblázat
        columns = ('Eszköz', 'IP', 'ID', 'Állapot', 'Válasz', 'Utolsó szkennelés')
        self.result_tree = ttk.Treeview(devices_frame, columns=columns, show='headings', height=15)

        # Oszlop beállítások
        column_widths = {'Eszköz': 200, 'IP': 120, 'ID': 80, 'Állapot': 100, 'Válasz': 150, 'Utolsó szkennelés': 130}
        for col in columns:
            self.result_tree.heading(col, text=col, command=lambda c=col: self.sort_column(c))
            self.result_tree.column(col, width=column_widths.get(col, 100))

        # Scrollbar
        tree_scroll = ttk.Scrollbar(devices_frame, orient='vertical', command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=tree_scroll.set)

        self.result_tree.pack(side='left', fill='both', expand=True)
        tree_scroll.pack(side='right', fill='y')

        # Jobb klikk menü
        self.create_context_menu()

        # Exportálás
        export_frame = ttk.Frame(self.results_frame)
        export_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(export_frame, text="📊 Excel exportálás", command=self.export_excel).pack(side='left', padx=2)
        ttk.Button(export_frame, text="📄 CSV exportálás", command=self.export_csv).pack(side='left', padx=2)
        ttk.Button(export_frame, text="📧 Email küldés", command=self.send_email).pack(side='left', padx=2)

    def setup_ip_manager_tab(self):
        """IP kezelő fül"""
        # Főcím
        title_frame = ttk.Frame(self.ip_manager_frame)
        title_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(title_frame, text="📝 IP Lista Karbantartás",
                 font=('Arial', 14, 'bold')).pack(side='left')

        # Eszköztár
        toolbar_frame = ttk.Frame(self.ip_manager_frame)
        toolbar_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(toolbar_frame, text="🔄 Frissítés", command=self.load_ip_data).pack(side='left', padx=2)
        ttk.Button(toolbar_frame, text="💾 Mentés", command=self.save_ip_data).pack(side='left', padx=2)
        ttk.Button(toolbar_frame, text="➕ Új eszköz", command=self.add_new_device).pack(side='left', padx=2)
        ttk.Button(toolbar_frame, text="❌ Törlés", command=self.delete_selected_device).pack(side='left', padx=2)
        ttk.Button(toolbar_frame, text="📋 Importálás", command=self.import_devices).pack(side='left', padx=2)

        # Keresés
        search_frame = ttk.Frame(self.ip_manager_frame)
        search_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(search_frame, text="🔍 Keresés:").pack(side='left')
        self.ip_search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.ip_search_var, width=20)
        search_entry.pack(side='left', padx=5)
        search_entry.bind('<KeyRelease>', self.filter_ip_list)
        ttk.Button(search_frame, text="🗑️", command=self.clear_ip_search).pack(side='left', padx=2)

        # IP lista táblázat
        list_frame = ttk.LabelFrame(self.ip_manager_frame, text="🖨️ Eszköz Lista", padding=5)
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Táblázat oszlopok
        columns = ('Eszköz név', 'IP cím', 'Eszköz ID', 'Informatikus', 'Email', 'Aktív')
        self.ip_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=20)

        # Oszlop beállítások
        column_widths = {
            'Eszköz név': 200, 'IP cím': 120, 'Eszköz ID': 100,
            'Informatikus': 120, 'Email': 200, 'Aktív': 60
        }

        for col in columns:
            self.ip_tree.heading(col, text=col)
            self.ip_tree.column(col, width=column_widths.get(col, 100))

        # Scrollbar
        ip_scroll = ttk.Scrollbar(list_frame, orient='vertical', command=self.ip_tree.yview)
        self.ip_tree.configure(yscrollcommand=ip_scroll.set)

        self.ip_tree.pack(side='left', fill='both', expand=True)
        ip_scroll.pack(side='right', fill='y')

        # Dupla klikk szerkesztéshez
        self.ip_tree.bind('<Double-1>', self.edit_selected_device)

        # Szerkesztő panel
        edit_frame = ttk.LabelFrame(self.ip_manager_frame, text="✏️ Eszköz Szerkesztés", padding=5)
        edit_frame.pack(fill='x', padx=10, pady=5)

        # Szerkesztő mezők
        fields_frame = ttk.Frame(edit_frame)
        fields_frame.pack(fill='x')

        # Bal oldali mezők
        left_frame = ttk.Frame(fields_frame)
        left_frame.pack(side='left', fill='both', expand=True, padx=5)

        ttk.Label(left_frame, text="Eszköz név:").grid(row=0, column=0, sticky='w', pady=2)
        self.edit_device_name_var = tk.StringVar()
        ttk.Entry(left_frame, textvariable=self.edit_device_name_var, width=25).grid(row=0, column=1, sticky='ew', padx=5, pady=2)

        ttk.Label(left_frame, text="IP cím:").grid(row=1, column=0, sticky='w', pady=2)
        self.edit_ip_var = tk.StringVar()
        ttk.Entry(left_frame, textvariable=self.edit_ip_var, width=25).grid(row=1, column=1, sticky='ew', padx=5, pady=2)

        ttk.Label(left_frame, text="Eszköz ID:").grid(row=2, column=0, sticky='w', pady=2)
        self.edit_device_id_var = tk.StringVar()
        ttk.Entry(left_frame, textvariable=self.edit_device_id_var, width=25).grid(row=2, column=1, sticky='ew', padx=5, pady=2)

        # Jobb oldali mezők
        right_frame = ttk.Frame(fields_frame)
        right_frame.pack(side='right', fill='both', expand=True, padx=5)

        ttk.Label(right_frame, text="Informatikus:").grid(row=0, column=0, sticky='w', pady=2)
        self.edit_specialist_var = tk.StringVar()
        ttk.Entry(right_frame, textvariable=self.edit_specialist_var, width=25).grid(row=0, column=1, sticky='ew', padx=5, pady=2)

        ttk.Label(right_frame, text="Email:").grid(row=1, column=0, sticky='w', pady=2)
        self.edit_email_var = tk.StringVar()
        ttk.Entry(right_frame, textvariable=self.edit_email_var, width=25).grid(row=1, column=1, sticky='ew', padx=5, pady=2)

        ttk.Label(right_frame, text="Aktív:").grid(row=2, column=0, sticky='w', pady=2)
        self.edit_active_var = tk.StringVar(value="1")
        active_combo = ttk.Combobox(right_frame, textvariable=self.edit_active_var,
                                   values=["1", "0"], width=22, state='readonly')
        active_combo.grid(row=2, column=1, sticky='ew', padx=5, pady=2)

        # Grid oszlop konfigurálás
        left_frame.grid_columnconfigure(1, weight=1)
        right_frame.grid_columnconfigure(1, weight=1)

        # Szerkesztő gombok
        edit_buttons_frame = ttk.Frame(edit_frame)
        edit_buttons_frame.pack(fill='x', pady=5)

        ttk.Button(edit_buttons_frame, text="💾 Mentés", command=self.save_device_changes).pack(side='left', padx=2)
        ttk.Button(edit_buttons_frame, text="🗑️ Törlés", command=self.clear_edit_fields).pack(side='left', padx=2)
        ttk.Button(edit_buttons_frame, text="🔄 Visszaállítás", command=self.reset_edit_fields).pack(side='left', padx=2)

        # Adatok betöltése
        self.load_ip_data()

    def setup_results_tab(self):
        """Eredmények fül"""
        # Eszköz lista
        devices_frame = ttk.LabelFrame(self.results_frame, text="🖨️ Eszközök", padding=5)
        devices_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Szűrők
        filter_frame = ttk.Frame(devices_frame)
        filter_frame.pack(fill='x', pady=(0, 5))

        ttk.Label(filter_frame, text="Szűrés:").pack(side='left', padx=2)
        self.device_filter_var = tk.StringVar()
        ttk.Entry(filter_frame, textvariable=self.device_filter_var, width=20).pack(side='left', padx=2)
        ttk.Button(filter_frame, text="🔍", command=self.filter_devices).pack(side='left', padx=2)
        ttk.Button(filter_frame, text="🗑️", command=self.clear_device_filter).pack(side='left', padx=2)

        ttk.Label(filter_frame, text="Státusz:").pack(side='left', padx=(10, 2))
        self.status_filter_var = tk.StringVar(value="Minden")
        ttk.Combobox(filter_frame, textvariable=self.status_filter_var,
                    values=["Minden", "Sikeres", "Hiba", "Timeout"], width=10).pack(side='left', padx=2)

        # Eredmény táblázat
        columns = ('Eszköz', 'IP', 'ID', 'Állapot', 'Válasz', 'Utolsó szkennelés')
        self.result_tree = ttk.Treeview(devices_frame, columns=columns, show='headings', height=15)

        # Oszlop beállítások
        column_widths = {'Eszköz': 200, 'IP': 120, 'ID': 80, 'Állapot': 100, 'Válasz': 150, 'Utolsó szkennelés': 130}
        for col in columns:
            self.result_tree.heading(col, text=col, command=lambda c=col: self.sort_column(c))
            self.result_tree.column(col, width=column_widths.get(col, 100))

        # Scrollbar
        tree_scroll = ttk.Scrollbar(devices_frame, orient='vertical', command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=tree_scroll.set)

        self.result_tree.pack(side='left', fill='both', expand=True)
        tree_scroll.pack(side='right', fill='y')

        # Jobb klikk menü
        self.create_context_menu()

        # Exportálás
        export_frame = ttk.Frame(self.results_frame)
        export_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(export_frame, text="📊 Excel exportálás", command=self.export_excel).pack(side='left', padx=2)
        ttk.Button(export_frame, text="📄 CSV exportálás", command=self.export_csv).pack(side='left', padx=2)
        ttk.Button(export_frame, text="📧 Email küldés", command=self.send_email).pack(side='left', padx=2)

    def setup_status_bar(self):
        """Státuszsor"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill='x', side='bottom')

        self.status_label = ttk.Label(self.status_frame, text="Kész", relief='sunken', anchor='w')
        self.status_label.pack(side='left', fill='x', expand=True, padx=2, pady=2)

        self.time_label = ttk.Label(self.status_frame, text="", relief='sunken')
        self.time_label.pack(side='right', padx=2, pady=2)

        self.update_time()

    def update_time(self):
        """Idő frissítése"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    # Eseménykezelők
    def browse_ip_file(self):
        filename = filedialog.askopenfilename(
            title="IP lista fájl kiválasztása",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            initialdir=WORK_DIRECTORY
        )
        if filename:
            self.ip_file_var.set(filename)

    def open_work_folder(self):
        try:
            os.startfile(WORK_DIRECTORY)
        except:
            messagebox.showerror("Hiba", f"Nem sikerült megnyitni: {WORK_DIRECTORY}")

    def save_settings(self):
        """Beállítások mentése"""
        self.config.update({
            "ip_file": self.ip_file_var.get(),
            "specialist": self.specialist_var.get(),
            "debug": self.debug_var.get(),
            "email": self.email_var.get(),
            "excel": self.excel_var.get(),
            "timeout": self.timeout_var.get(),
            "max_concurrent": self.concurrent_var.get()
        })
        self.save_config()
        messagebox.showinfo("Siker", "Beállítások mentve!")

    def refresh_device_list(self):
        """Eszköz lista frissítése"""
        try:
            devices = []
            ip_file_path = f"{WORK_DIRECTORY}{IP_LIST_FILE_NAME}"

            if os.path.exists(ip_file_path):
                with open(ip_file_path, 'r', encoding='utf-8') as file:
                    for line in file:
                        parts = line.strip().split('\t')
                        if len(parts) >= 6 and parts[5] == '1':  # Aktív eszközök
                            device_name = parts[0]
                            ip_address = parts[1]
                            device_display = f"{device_name} ({ip_address})"
                            devices.append(device_display)

                # Combobox frissítése
                if hasattr(self, 'test_device_combo') and self.test_device_combo:
                    self.test_device_combo['values'] = tuple(devices)  # Tuple formátum
                    self.test_device_combo['state'] = 'readonly'

                    if devices:
                        self.test_device_combo.current(0)  # Első elem kiválasztása
                        self.log_message(f"📋 {len(devices)} eszköz betöltve a teszteléshez", "INFO")
                    else:
                        self.log_message("⚠️ Nincs aktív eszköz a listában", "WARNING")
                else:
                    self.log_message("❌ Eszköz combobox nem elérhető", "ERROR")
            else:
                self.log_message(f"❌ IP lista fájl nem található: {ip_file_path}", "ERROR")

        except Exception as e:
            self.log_message(f"❌ Eszköz lista betöltési hiba: {e}", "ERROR")

    def get_selected_device_ip(self):
        """Kiválasztott eszköz IP címének kinyerése"""
        device_text = self.test_device_var.get()

        if not device_text:
            return None

        # "Device Name (IP)" formátumból IP kinyerése
        try:
            ip = device_text.split('(')[1].split(')')[0]
            return ip
        except Exception as e:
            return None

    def ping_test(self):
        """Ping teszt kiválasztott eszközre"""
        ip = self.get_selected_device_ip()
        if not ip:
            messagebox.showerror("Hiba", "Válasszon ki egy eszközt a listából!")
            return

        device_name = self.test_device_var.get().split('(')[0].strip()
        self.log_message(f"🏓 Ping teszt indítása: {device_name} ({ip})", "INFO")

        def run_ping():
            import subprocess
            try:
                result = subprocess.run(['ping', '-n', '1', ip],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.message_queue.put(("log", f"✅ Ping sikeres: {device_name} ({ip})", "SUCCESS"))
                else:
                    self.message_queue.put(("log", f"❌ Ping sikertelen: {device_name} ({ip})", "ERROR"))
            except Exception as e:
                self.message_queue.put(("log", f"❌ Ping hiba: {device_name} ({ip}) - {e}", "ERROR"))

        threading.Thread(target=run_ping, daemon=True).start()

    def ping_manual(self):
        """Manuális IP ping teszt"""
        ip = self.test_ip_var.get()
        if not ip:
            messagebox.showerror("Hiba", "Adjon meg egy IP címet!")
            return

        def run_ping():
            import subprocess
            try:
                result = subprocess.run(['ping', '-n', '1', ip],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.message_queue.put(("log", f"✅ Ping sikeres: {ip}", "SUCCESS"))
                else:
                    self.message_queue.put(("log", f"❌ Ping sikertelen: {ip}", "ERROR"))
            except Exception as e:
                self.message_queue.put(("log", f"❌ Ping hiba: {e}", "ERROR"))

        threading.Thread(target=run_ping, daemon=True).start()

    def snmp_test(self):
        """SNMP teszt kiválasztott eszközre"""
        ip = self.get_selected_device_ip()
        if not ip:
            messagebox.showerror("Hiba", "Válasszon ki egy eszközt a listából!")
            return

        device_name = self.test_device_var.get().split('(')[0].strip()
        self.log_message(f"🔍 SNMP teszt indítása: {device_name} ({ip})", "INFO")

        def run_snmp_test():
            try:
                # SNMP teszt a kiválasztott eszközre
                test_printer = [device_name, ip, "TEST001", "Informatikus", "<EMAIL>", "1"]
                snmp_getter = SnmpGetter()

                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(snmp_getter.scan_printer(test_printer))

                if "Sikeres" in result[1]:
                    self.message_queue.put(("log", f"✅ SNMP teszt sikeres: {device_name} ({ip})", "SUCCESS"))
                    self.message_queue.put(("log", f"📊 SNMP válasz: {result[1]}", "INFO"))
                else:
                    self.message_queue.put(("log", f"❌ SNMP teszt sikertelen: {device_name} ({ip}) - {result[1]}", "ERROR"))

            except Exception as e:
                self.message_queue.put(("log", f"❌ SNMP teszt hiba: {device_name} ({ip}) - {e}", "ERROR"))

        threading.Thread(target=run_snmp_test, daemon=True).start()

    def snmp_manual(self):
        """Manuális IP SNMP teszt"""
        ip = self.test_ip_var.get()
        if not ip:
            messagebox.showerror("Hiba", "Adjon meg egy IP címet!")
            return

        def run_snmp_test():
            try:
                # Manuális SNMP teszt
                test_printer = ["Manual Test", ip, "MANUAL001", "Informatikus", "<EMAIL>", "1"]
                snmp_getter = SnmpGetter()

                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(snmp_getter.scan_printer(test_printer))

                if "Sikeres" in result[1]:
                    self.message_queue.put(("log", f"✅ SNMP teszt sikeres: {ip}", "SUCCESS"))
                    self.message_queue.put(("log", f"📊 SNMP válasz: {result[1]}", "INFO"))
                else:
                    self.message_queue.put(("log", f"❌ SNMP teszt sikertelen: {ip} - {result[1]}", "ERROR"))

            except Exception as e:
                self.message_queue.put(("log", f"❌ SNMP teszt hiba: {e}", "ERROR"))

        threading.Thread(target=run_snmp_test, daemon=True).start()

    def refresh_ip_list(self):
        """IP lista frissítése (ip funkció)"""
        if messagebox.askyesno("IP lista frissítése",
                              "Frissíti az IP listát új címekkel?\n\n"
                              "Ez a new_ip_import.refresh_printers() funkciót hívja meg."):
            try:
                from new_ip_import import NewIpImport
                NewIpImport().refresh_printers()
                messagebox.showinfo("Siker", "IP lista sikeresen frissítve!")
                self.log_message("✅ IP lista frissítve", "SUCCESS")
            except Exception as e:
                messagebox.showerror("Hiba", f"IP lista frissítési hiba:\n{e}")
                self.log_message(f"❌ IP lista frissítési hiba: {e}", "ERROR")

    def erase_database(self):
        """Adatbázis törlése (erase funkció)"""
        if messagebox.askyesno("Adatbázis törlése",
                              "⚠️ FIGYELEM! ⚠️\n\n"
                              "Ez törli az összes korábban gyűjtött adatot az adatbázisból!\n\n"
                              "Biztosan folytatja?"):
            try:
                from database import sql_connect
                from global_parameters import WORK_DIRECTORY

                # Adatbázis kapcsolat létrehozása
                db = sql_connect()

                # Táblák törlése
                try:
                    db.execute('DELETE FROM printers')
                    self.log_message("✅ Printers tábla törölve", "SUCCESS")
                except Exception as table_error:
                    self.log_message(f"⚠️ Printers tábla törlési hiba: {table_error}", "WARNING")

                # Egyéb táblák törlése (ha vannak)
                try:
                    db.execute('DELETE FROM devices')
                    self.log_message("✅ Devices tábla törölve", "SUCCESS")
                except Exception:
                    pass  # Lehet, hogy nincs ilyen tábla

                messagebox.showinfo("Siker", "Adatbázis sikeresen törölve!")
                self.log_message("✅ Adatbázis törlés befejezve (erase)", "SUCCESS")

            except Exception as e:
                messagebox.showerror("Hiba", f"Adatbázis törlési hiba:\n{e}")
                self.log_message(f"❌ Adatbázis törlési hiba: {e}", "ERROR")

    def release_all_data(self):
        """Teljes adattörlés (release funkció)"""
        if messagebox.askyesno("Teljes adattörlés",
                              "⚠️ VESZÉLYES MŰVELET! ⚠️\n\n"
                              "Ez törli:\n"
                              "• Az összes adatbázis adatot\n"
                              "• A szkennelt eszközök listáját\n"
                              "• Az összes work fájlt\n\n"
                              "Ez a 'release' funkció!\n\n"
                              "Biztosan folytatja?"):
            try:
                from database import sql_connect
                from global_parameters import WORK_DIRECTORY
                import glob

                # 1. Adatbázis törlése
                try:
                    db = sql_connect()
                    db.execute('DELETE FROM printers')
                    db.execute('DELETE FROM devices')
                    self.log_message("✅ Adatbázis táblák törölve", "SUCCESS")
                except Exception as db_error:
                    self.log_message(f"⚠️ Adatbázis törlési hiba: {db_error}", "WARNING")

                # 2. Szkennelt eszközök fájl törlése/ürítése
                try:
                    scanned_file = f"{WORK_DIRECTORY}scanned_printer.txt"
                    with open(scanned_file, "w", encoding='utf-8') as file:
                        file.write("")  # Üres fájl
                    self.log_message("✅ Szkennelt eszközök lista törölve", "SUCCESS")
                except Exception as file_error:
                    self.log_message(f"⚠️ Szkennelt fájl törlési hiba: {file_error}", "WARNING")

                # 3. Régi válasz fájlok törlése (opcionális)
                try:
                    answer_files = glob.glob(f"{WORK_DIRECTORY}answers-*.txt")
                    bad_answer_files = glob.glob(f"{WORK_DIRECTORY}bad_answers-*.txt")

                    deleted_count = 0
                    for file in answer_files + bad_answer_files:
                        try:
                            os.remove(file)
                            deleted_count += 1
                        except:
                            pass

                    if deleted_count > 0:
                        self.log_message(f"✅ {deleted_count} régi válasz fájl törölve", "SUCCESS")

                except Exception as cleanup_error:
                    self.log_message(f"⚠️ Fájl tisztítási hiba: {cleanup_error}", "WARNING")

                messagebox.showinfo("Siker",
                                   "Teljes adattörlés befejezve!\n\n"
                                   "• Adatbázis törölve\n"
                                   "• Szkennelt eszközök listája törölve\n"
                                   "• Régi fájlok törölve")
                self.log_message("✅ Teljes adattörlés befejezve (release)", "SUCCESS")

            except Exception as e:
                messagebox.showerror("Hiba", f"Teljes törlési hiba:\n{e}")
                self.log_message(f"❌ Teljes törlési hiba: {e}", "ERROR")

    def analyse_data(self):
        """Adatelemzés (analyse funkció)"""
        if messagebox.askyesno("Adatelemzés",
                              "Elindítja az adatelemzést?\n\n"
                              "Ez elemzi az adatbázisban tárolt adatokat."):
            try:
                import analyse_data

                # Háttérben futtatjuk
                def run_analyse():
                    try:
                        analyse_data.run()
                        self.message_queue.put(("log", "✅ Adatelemzés befejezve", "SUCCESS"))
                        messagebox.showinfo("Siker", "Adatelemzés sikeresen befejezve!")
                    except Exception as e:
                        self.message_queue.put(("log", f"❌ Adatelemzési hiba: {e}", "ERROR"))
                        messagebox.showerror("Hiba", f"Adatelemzési hiba:\n{e}")

                self.log_message("🔍 Adatelemzés indítása...", "INFO")
                threading.Thread(target=run_analyse, daemon=True).start()

            except ImportError:
                messagebox.showerror("Hiba", "Az analyse_data modul nem található!")
                self.log_message("❌ analyse_data modul hiányzik", "ERROR")
            except Exception as e:
                messagebox.showerror("Hiba", f"Adatelemzési hiba:\n{e}")
                self.log_message(f"❌ Adatelemzési hiba: {e}", "ERROR")

    def export_sql(self):
        """SQL export (sql funkció)"""
        if messagebox.askyesno("SQL Export",
                              "Exportálja az adatbázis adatokat SQL fájlba?\n\n"
                              "Ez létrehozza az SQL dump fájlt."):
            try:
                import save_sql_command

                # Háttérben futtatjuk
                def run_sql_export():
                    try:
                        save_sql_command.run()
                        self.message_queue.put(("log", "✅ SQL export befejezve", "SUCCESS"))
                        messagebox.showinfo("Siker", "SQL export sikeresen befejezve!")
                    except Exception as e:
                        self.message_queue.put(("log", f"❌ SQL export hiba: {e}", "ERROR"))
                        messagebox.showerror("Hiba", f"SQL export hiba:\n{e}")

                self.log_message("📄 SQL export indítása...", "INFO")
                threading.Thread(target=run_sql_export, daemon=True).start()

            except ImportError:
                messagebox.showerror("Hiba", "A save_sql_command modul nem található!")
                self.log_message("❌ save_sql_command modul hiányzik", "ERROR")
            except Exception as e:
                messagebox.showerror("Hiba", f"SQL export hiba:\n{e}")
                self.log_message(f"❌ SQL export hiba: {e}", "ERROR")

    def clear_logs(self):
        """Log fájlok törlése"""
        if messagebox.askyesno("Megerősítés", "Biztosan törli az összes log fájlt?"):
            try:
                import glob
                log_files = glob.glob(f"{WORK_DIRECTORY}*.log")
                for file in log_files:
                    os.remove(file)
                messagebox.showinfo("Siker", f"{len(log_files)} log fájl törölve!")
            except Exception as e:
                messagebox.showerror("Hiba", f"Log törlési hiba: {e}")

    def show_help(self):
        """Súgó megjelenítése"""
        help_text = """
🖨️ SNMP Nyomtató Scanner Pro - Súgó

FUNKCIÓK:
• SNMP alapú nyomtató adatgyűjtés
• Többszálú, aszinkron szkennelés
• Excel és CSV exportálás
• Email értesítések
• Részletes logging és hibakezelés

HASZNÁLAT:
1. Állítsa be az IP lista fájlt a Beállítások fülön
2. Válassza ki a kívánt opciókat
3. Indítsa el a szkennelést a Szkennelés fülön
4. Tekintse meg az eredményeket az Eredmények fülön

TIPPEK:
• Használja a teszt funkciókat a beállítások ellenőrzésére
• A debug mód részletes információkat ad
• A timeout értéket állítsa be a hálózat sebességének megfelelően
        """

        help_window = tk.Toplevel(self.root)
        help_window.title("Súgó")
        help_window.geometry("500x400")

        text_widget = scrolledtext.ScrolledText(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill='both', expand=True)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state='disabled')

    def show_about(self):
        """Névjegy"""
        about_text = """
🖨️ SNMP Nyomtató Scanner Pro
Verzió: 2.0

Fejlesztő: AI Assistant
Licenc: MIT

Köszönet:
• pysnmp könyvtár
• tkinter GUI framework
• Python közösség
        """
        messagebox.showinfo("Névjegy", about_text)

    # Szkennelés funkciók (egyszerűsített verzió)
    def start_scan(self):
        """Szkennelés indítása"""
        if self.is_scanning:
            return

        if not os.path.exists(self.ip_file_var.get()):
            messagebox.showerror("Hiba", "Az IP lista fájl nem található!")
            return

        self.is_scanning = True
        self.scan_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.pause_button.config(state='normal')

        self.log_message("🚀 SNMP szkennelés indítása...", "INFO")
        self.status_label.config(text="Szkennelés folyamatban...")

        # Szkennelés indítása külön szálban
        self.scan_thread = threading.Thread(target=self.run_scan_thread, daemon=True)
        self.scan_thread.start()

    def stop_scan(self):
        """Szkennelés leállítása"""
        self.is_scanning = False
        self.log_message("⏹️ Szkennelés leállítása...", "WARNING")
        self.status_label.config(text="Szkennelés leállítva")

        self.scan_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.pause_button.config(state='disabled')

    def pause_scan(self):
        """Szkennelés szüneteltetése"""
        # Implementálható a jövőben
        messagebox.showinfo("Info", "Szünet funkció fejlesztés alatt...")

    def run_scan_thread(self):
        """Szkennelés futtatása külön szálban"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.run_snmp_scan())
        except Exception as e:
            self.message_queue.put(("log", f"❌ Szkennelési hiba: {e}", "ERROR"))
        finally:
            self.message_queue.put(("scan_finished",))

    async def run_snmp_scan(self):
        """Aszinkron SNMP szkennelés"""
        try:
            # Debug beállítása
            if self.debug_var.get():
                from pysnmp import debug
                debug.set_logger(debug.Debug('all'))

            # SNMP getter létrehozása
            self.snmp_getter = SnmpGetter()

            # IP lista beolvasása
            printers = self.load_ip_list()
            if not printers:
                self.message_queue.put(("log", "❌ Üres IP lista!", "ERROR"))
                return

            total_count = len(printers)
            self.message_queue.put(("log", f"📊 {total_count} eszköz betöltve", "INFO"))

            # Párhuzamos szkennelés (mint az eredeti konzol verzióban)
            self.message_queue.put(("log", "🚀 Párhuzamos szkennelés indítása...", "INFO"))

            # Eredeti SnmpGetter run() metódus használata
            self.snmp_getter.printers = printers
            await self.snmp_getter.run()

            # Eredmények feldolgozása
            success_count = len(self.snmp_getter.answers_from_printer)
            failed_count = len(self.snmp_getter.bad_answers_from_printer)

            # Sikeres válaszok megjelenítése
            for answer in self.snmp_getter.answers_from_printer:
                device_name = answer[0] if len(answer) > 0 else "Ismeretlen"
                ip = answer[1] if len(answer) > 1 else "Ismeretlen IP"
                self.message_queue.put(("result", device_name, ip, "✅ Sikeres", "SNMP válasz érkezett", datetime.now().strftime("%H:%M:%S")))

            # Hibás válaszok megjelenítése
            for bad_answer in self.snmp_getter.bad_answers_from_printer:
                device_name = bad_answer[0] if len(bad_answer) > 0 else "Ismeretlen"
                ip = bad_answer[1] if len(bad_answer) > 1 else "Ismeretlen IP"
                self.message_queue.put(("result", device_name, ip, "❌ Hiba", "Nincs SNMP válasz", datetime.now().strftime("%H:%M:%S")))

            # Végső statisztikák
            self.message_queue.put(("progress", 100, success_count, failed_count, total_count))

            if self.is_scanning:
                self.message_queue.put(("log", f"✅ Szkennelés befejezve! Sikeres: {success_count}, Sikertelen: {failed_count}", "SUCCESS"))

        except Exception as e:
            self.message_queue.put(("log", f"❌ Kritikus hiba: {e}", "ERROR"))

    def load_ip_list(self):
        """IP lista betöltése"""
        try:
            printers = []
            specialist_filter = self.specialist_var.get()

            with open(self.ip_file_var.get(), 'r', encoding='utf-8') as file:
                for line in file:
                    parts = line.strip().split('\t')
                    if len(parts) >= 6 and parts[5] == '1':
                        if specialist_filter == "Minden" or parts[3] == specialist_filter:
                            printers.append(parts)
            return printers
        except Exception as e:
            self.message_queue.put(("log", f"❌ IP lista hiba: {e}", "ERROR"))
            return []

    # Segédfunkciók
    def log_message(self, message, level="INFO"):
        """Log üzenet hozzáadása"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)

    def clear_log(self):
        """Log törlése"""
        self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """Log mentése"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("Siker", "Log mentve!")
            except Exception as e:
                messagebox.showerror("Hiba", f"Log mentési hiba: {e}")

    def filter_log(self, event=None):
        """Log szűrése"""
        # Implementálható a jövőben
        pass

    def filter_devices(self):
        """Eszközök szűrése név/IP és státusz alapján"""
        try:
            filter_text = self.device_filter_var.get().lower().strip()
            status_filter = self.status_filter_var.get()

            # Aktuális treeview tartalom mentése (ha nincs all_results)
            if not hasattr(self, 'all_results') or not self.all_results:
                self.all_results = []
                for item in self.result_tree.get_children():
                    values = self.result_tree.item(item, 'values')
                    if values:
                        self.all_results.append(values)

            # Ha még mindig nincs adat
            if not self.all_results:
                self.log_message("⚠️ Nincs adat a szűréshez", "WARNING")
                return

            # Összes elem törlése a treeview-ból
            for item in self.result_tree.get_children():
                self.result_tree.delete(item)

            # Szűrés végrehajtása
            filtered_results = []
            for result in self.all_results:
                if len(result) < 2:
                    continue

                device_name = str(result[0]).lower() if result[0] else ''
                ip_address = str(result[1]).lower() if result[1] else ''
                status = str(result[3]).lower() if len(result) > 3 else ''

                # Szöveg szűrés
                text_match = True
                if filter_text:
                    text_match = (filter_text in device_name or filter_text in ip_address)

                # Státusz szűrés
                status_match = True
                if status_filter and status_filter != "Minden":
                    if status_filter == "Sikeres":
                        status_match = "sikeres" in status
                    elif status_filter == "Hiba":
                        status_match = "hiba" in status or "error" in status
                    elif status_filter == "Timeout":
                        status_match = "timeout" in status

                # Ha mindkét feltétel teljesül
                if text_match and status_match:
                    filtered_results.append(result)

            # Szűrt eredmények megjelenítése
            for result in filtered_results:
                self.result_tree.insert('', 'end', values=result)

            # Eredmény log
            total_count = len(self.all_results)
            filtered_count = len(filtered_results)
            self.log_message(f"🔍 Szűrés: {filtered_count} találat ({total_count} összesen)", "INFO")

        except Exception as e:
            self.log_message(f"❌ Szűrési hiba: {e}", "ERROR")

    def clear_device_filter(self):
        """Szűrés törlése"""
        self.device_filter_var.set("")
        self.status_filter_var.set("Minden")

        # Összes eredmény visszaállítása
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        if hasattr(self, 'all_results') and self.all_results:
            for result in self.all_results:
                self.result_tree.insert('', 'end', values=result)
            self.log_message("🗑️ Szűrés törölve, összes eredmény megjelenítve", "INFO")

    def sort_column(self, col):
        """Oszlop rendezése"""
        items = [(self.result_tree.set(item, col), item) for item in self.result_tree.get_children('')]
        items.sort()

        for index, (val, item) in enumerate(items):
            self.result_tree.move(item, '', index)

        self.log_message(f"📊 Rendezés: {col} oszlop szerint", "INFO")

    def check_queue(self):
        """Üzenetek feldolgozása"""
        try:
            while True:
                message = self.message_queue.get_nowait()

                if message[0] == "log":
                    self.log_message(message[1], message[2] if len(message) > 2 else "INFO")
                elif message[0] == "result":
                    self.add_result(*message[1:])
                elif message[0] == "progress":
                    self.update_progress(*message[1:])
                elif message[0] == "scan_finished":
                    self.scan_button.config(state='normal')
                    self.stop_button.config(state='disabled')
                    self.pause_button.config(state='disabled')
                    self.is_scanning = False
                    self.status_label.config(text="Szkennelés befejezve")

        except queue.Empty:
            pass

        self.root.after(100, self.check_queue)

    def add_result(self, device_name, ip, status, response, timestamp):
        """Eredmény hozzáadása"""
        result_data = (device_name, ip, "N/A", status, response, timestamp)

        # Eredmény hozzáadása a táblázathoz
        self.result_tree.insert('', 'end', values=result_data)

        # Eredmények tárolása a szűréshez
        if not hasattr(self, 'all_results'):
            self.all_results = []
        self.all_results.append(result_data)

    def update_progress(self, progress, success, failed, total):
        """Progress frissítése"""
        self.progress_var.set(progress)
        self.total_label.config(text=str(total))
        self.success_label.config(text=str(success))
        self.failed_label.config(text=str(failed))

    # Eredmények fül funkciók
    def filter_devices(self):
        """Eszközök szűrése"""
        filter_text = self.device_filter_var.get().lower()
        if not filter_text:
            messagebox.showinfo("Szűrés", "Adjon meg szűrési feltételt!")
            return

        # Összes elem elrejtése
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # Szűrt elemek megjelenítése (ha van adat)
        if hasattr(self, 'all_results'):
            filtered_results = [
                result for result in self.all_results
                if filter_text in result[0].lower() or filter_text in result[1].lower()
            ]

            for result in filtered_results:
                self.result_tree.insert('', 'end', values=result)

            self.log_message(f"🔍 Szűrés alkalmazva: {len(filtered_results)} találat", "INFO")
        else:
            self.log_message("⚠️ Nincs adat a szűréshez", "WARNING")

    def clear_device_filter(self):
        """Szűrés törlése"""
        self.device_filter_var.set("")

        # Összes eredmény visszaállítása
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        if hasattr(self, 'all_results'):
            for result in self.all_results:
                self.result_tree.insert('', 'end', values=result)
            self.log_message("🗑️ Szűrés törölve", "INFO")

    def sort_column(self, col):
        """Oszlop rendezése"""
        items = [(self.result_tree.set(item, col), item) for item in self.result_tree.get_children('')]
        items.sort()

        for index, (val, item) in enumerate(items):
            self.result_tree.move(item, '', index)

        self.log_message(f"📊 Rendezés: {col}", "INFO")

    def create_context_menu(self):
        """Jobb klikk menü létrehozása"""
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="Másolás", command=self.copy_selected)
        context_menu.add_command(label="Részletek", command=self.show_details)
        context_menu.add_separator()
        context_menu.add_command(label="Ping teszt", command=self.ping_selected)

        def show_context_menu(event):
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        self.result_tree.bind("<Button-3>", show_context_menu)

    def copy_selected(self):
        """Kiválasztott elem másolása"""
        selection = self.result_tree.selection()
        if selection:
            item = selection[0]
            values = self.result_tree.item(item, 'values')
            text = '\t'.join(values)
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            self.log_message("📋 Elem vágólapra másolva", "SUCCESS")

    def show_details(self):
        """Kiválasztott elem részletei"""
        selection = self.result_tree.selection()
        if selection:
            item = selection[0]
            values = self.result_tree.item(item, 'values')

            details = f"""
Eszköz Részletek:

Név: {values[0] if len(values) > 0 else 'N/A'}
IP cím: {values[1] if len(values) > 1 else 'N/A'}
ID: {values[2] if len(values) > 2 else 'N/A'}
Állapot: {values[3] if len(values) > 3 else 'N/A'}
Válasz: {values[4] if len(values) > 4 else 'N/A'}
Utolsó szkennelés: {values[5] if len(values) > 5 else 'N/A'}
            """

            messagebox.showinfo("Eszköz Részletek", details)

    def ping_selected(self):
        """Kiválasztott eszköz ping tesztelése"""
        selection = self.result_tree.selection()
        if selection:
            item = selection[0]
            values = self.result_tree.item(item, 'values')
            ip = values[1] if len(values) > 1 else None

            if ip:
                self.test_ip_var.set(ip)
                self.ping_test()

    def export_excel(self):
        """Excel exportálás (eredeti analyse_data.py alapján)"""
        try:
            # Ellenőrizzük, hogy van-e adat az adatbázisban
            def check_database():
                try:
                    from database import sql_connect
                    conn = sql_connect()
                    cursor = conn.cursor
                    cursor.execute('SELECT COUNT(*) FROM printers')
                    count = cursor.fetchone()[0]
                    return count > 0
                except Exception:
                    return False

            has_db_data = check_database()
            has_snmp_data = (hasattr(self, 'snmp_getter') and self.snmp_getter and
                           (self.snmp_getter.answers_from_printer or self.snmp_getter.bad_answers_from_printer))

            if not has_db_data and not has_snmp_data:
                messagebox.showwarning("Figyelem",
                                     "Nincs adat az Excel exportáláshoz!\n\n"
                                     "Először futtasson szkennelést, hogy adatok kerüljenek az adatbázisba.")
                return

            # Választási lehetőség
            if has_db_data:
                choice = messagebox.askyesnocancel("Excel Export Típusa",
                                                  "Milyen Excel fájlt szeretne létrehozni?\n\n"
                                                  "IGEN = Eredeti 'Analyzing' Excel (adatbázisból)\n"
                                                  "NEM = Egyszerű SNMP eredmények Excel\n"
                                                  "MÉGSE = Lemondás")

                if choice is None:  # Cancel
                    return
                elif choice:  # Yes - Eredeti Analyzing Excel
                    self.create_analyzing_excel()
                else:  # No - Egyszerű Excel
                    self.create_simple_excel()
            else:
                # Csak egyszerű Excel lehetséges
                if messagebox.askyesno("Excel Export",
                                      "Létrehozza az SNMP eredmények Excel fájlt?\n\n"
                                      "Ez a legutóbbi szkennelés eredményeit tartalmazza."):
                    self.create_simple_excel()

        except Exception as e:
            messagebox.showerror("Hiba", f"Excel export hiba:\n{e}")
            self.log_message(f"❌ Excel export hiba: {e}", "ERROR")

    def create_analyzing_excel(self):
        """Eredeti Analyzing Excel létrehozása"""
        def create_excel():
            try:
                import analyse_data

                # Eredeti analyse_data.run() hívása
                analyse_data.run()

                from global_parameters import YEAR_MONTH_DAY_TIME, WORK_DIRECTORY
                excel_filename = f"Analyzing - {YEAR_MONTH_DAY_TIME}.xlsx"
                excel_path = f"{WORK_DIRECTORY}{excel_filename}"

                # Ellenőrizzük, hogy létrejött-e a fájl
                if os.path.exists(excel_path):
                    self.message_queue.put(("log", "✅ Analyzing Excel sikeresen létrehozva!", "SUCCESS"))
                    self.message_queue.put(("log", f"📁 Fájl helye: {excel_path}", "INFO"))

                    messagebox.showinfo("Siker",
                                      f"Analyzing Excel sikeresen létrehozva!\n\n"
                                      f"Fájl: {excel_filename}\n"
                                      f"Hely: work/ mappa")
                else:
                    self.message_queue.put(("log", "⚠️ Analyzing Excel nem jött létre (nincs adat?)", "WARNING"))
                    messagebox.showwarning("Figyelem",
                                         "Az Analyzing Excel nem jött létre.\n\n"
                                         "Lehetséges okok:\n"
                                         "• Nincs elég adat az adatbázisban\n"
                                         "• Adatbázis kapcsolati hiba")

            except ImportError as e:
                if "xlsxwriter" in str(e):
                    self.message_queue.put(("log", "❌ xlsxwriter csomag hiányzik", "ERROR"))
                    messagebox.showerror("Hiányzó csomag",
                                       "Az Excel exportáláshoz szükséges xlsxwriter csomag nincs telepítve!\n\n"
                                       "Telepítés: pip install xlsxwriter")
                else:
                    self.message_queue.put(("log", f"❌ Import hiba: {e}", "ERROR"))
                    messagebox.showerror("Import hiba", f"Modul betöltési hiba:\n{e}")

            except Exception as e:
                self.message_queue.put(("log", f"❌ Analyzing Excel hiba: {e}", "ERROR"))
                messagebox.showerror("Hiba", f"Analyzing Excel hiba:\n{e}")

        self.log_message("📊 Analyzing Excel indítása (analyse_data.run())...", "INFO")
        threading.Thread(target=create_excel, daemon=True).start()

    def create_simple_excel(self):
        """Egyszerű SNMP eredmények Excel létrehozása"""
        def create_excel():
            try:
                from excel_generator import create_excel_from_snmp_data

                if hasattr(self, 'snmp_getter') and self.snmp_getter:
                    specialist = self.specialist_var.get()
                    excel_path = create_excel_from_snmp_data(
                        self.snmp_getter.answers_from_printer,
                        self.snmp_getter.bad_answers_from_printer,
                        specialist
                    )

                    self.message_queue.put(("log", "✅ SNMP eredmények Excel létrehozva!", "SUCCESS"))
                    self.message_queue.put(("log", f"📁 Fájl helye: {excel_path}", "INFO"))

                    messagebox.showinfo("Siker",
                                      f"SNMP eredmények Excel létrehozva!\n\n"
                                      f"Fájl: {os.path.basename(excel_path)}\n"
                                      f"Hely: work/ mappa")
                else:
                    self.message_queue.put(("log", "❌ Nincs SNMP adat", "ERROR"))
                    messagebox.showerror("Hiba", "Nincs SNMP adat az exportáláshoz!")

            except Exception as e:
                self.message_queue.put(("log", f"❌ SNMP Excel hiba: {e}", "ERROR"))
                messagebox.showerror("Hiba", f"SNMP Excel hiba:\n{e}")

        self.log_message("📊 SNMP eredmények Excel indítása...", "INFO")
        threading.Thread(target=create_excel, daemon=True).start()

    def export_csv(self):
        """CSV exportálás"""
        try:
            items = self.result_tree.get_children()
            if not items:
                messagebox.showwarning("Figyelem", "Nincs adat az exportáláshoz!")
                return

            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="CSV fájl mentése"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8', newline='') as f:
                    import csv
                    writer = csv.writer(f, delimiter=';')

                    # Fejléc
                    headers = ['Eszköz', 'IP', 'ID', 'Állapot', 'Válasz', 'Utolsó szkennelés']
                    writer.writerow(headers)

                    # Adatok
                    for item in items:
                        values = self.result_tree.item(item, 'values')
                        writer.writerow(values)

                messagebox.showinfo("Siker", f"CSV fájl mentve:\n{filename}")
                self.log_message(f"📄 CSV export: {filename}", "SUCCESS")

        except Exception as e:
            messagebox.showerror("Hiba", f"CSV export hiba:\n{e}")
            self.log_message(f"❌ CSV export hiba: {e}", "ERROR")

    def send_email(self):
        """Email küldés"""
        try:
            if not hasattr(self, 'snmp_getter') or not self.snmp_getter:
                messagebox.showwarning("Figyelem", "Nincs adat az email küldéshez!\nElőször futtasson szkennelést.")
                return

            # Email paraméterek bekérése
            email_dialog = EmailDialog(self.root)
            if not email_dialog.result:
                return  # Felhasználó lemondta

            receiver_name = email_dialog.receiver_name
            receiver_email = email_dialog.receiver_email

            if messagebox.askyesno("Email küldés",
                                  f"Elküldi az eredményeket email-ben?\n\n"
                                  f"Címzett: {receiver_name}\n"
                                  f"Email: {receiver_email}\n\n"
                                  f"Ez a legutóbbi szkennelés eredményeit küldi el."):

                def send_email_thread():
                    try:
                        from email_sender import send_email
                        from global_parameters import YEAR_MONTH_DAY_TIME

                        specialist = self.specialist_var.get()

                        # Email küldés a helyes paraméterekkel
                        success = send_email(
                            receiver=receiver_name,
                            receiver_email=receiver_email,
                            year_month_for_sending=YEAR_MONTH_DAY_TIME,
                            it_specialist=specialist
                        )

                        if success:
                            self.message_queue.put(("log", f"📧 Email sikeresen elküldve: {receiver_email}", "SUCCESS"))
                            messagebox.showinfo("Siker", f"Email sikeresen elküldve!\n\nCímzett: {receiver_name}")
                        else:
                            self.message_queue.put(("log", "❌ Email küldés sikertelen", "ERROR"))
                            messagebox.showerror("Hiba", "Email küldés sikertelen!")

                    except Exception as e:
                        self.message_queue.put(("log", f"❌ Email küldési hiba: {e}", "ERROR"))
                        messagebox.showerror("Hiba", f"Email küldési hiba:\n{e}")

                self.log_message(f"📧 Email küldés indítása: {receiver_email}...", "INFO")
                threading.Thread(target=send_email_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("Hiba", f"Email küldési hiba:\n{e}")
            self.log_message(f"❌ Email küldési hiba: {e}", "ERROR")

    def open_work_folder(self):
        """Work mappa megnyitása"""
        try:
            work_path = os.path.abspath(WORK_DIRECTORY)

            if os.path.exists(work_path):
                # Windows Explorer megnyitása (több módszer)
                import subprocess
                import platform

                try:
                    if platform.system() == "Windows":
                        # Windows Explorer
                        os.startfile(work_path)
                    else:
                        # Linux/Mac
                        subprocess.run(['xdg-open', work_path], check=True)

                    self.log_message(f"📁 Work mappa megnyitva: {work_path}", "SUCCESS")

                except Exception as e:
                    # Fallback: subprocess explorer
                    try:
                        subprocess.run(['explorer', work_path.replace('/', '\\')], shell=True)
                        self.log_message(f"📁 Work mappa megnyitva (fallback): {work_path}", "SUCCESS")
                    except:
                        raise e

            else:
                messagebox.showerror("Hiba", f"Work mappa nem található:\n{work_path}")
                self.log_message(f"❌ Work mappa nem található: {work_path}", "ERROR")

        except Exception as e:
            messagebox.showerror("Hiba", f"Work mappa megnyitási hiba:\n{e}")
            self.log_message(f"❌ Work mappa megnyitási hiba: {e}", "ERROR")

    def browse_ip_file(self):
        """IP lista fájl megnyitása"""
        try:
            ip_file_path = f"{WORK_DIRECTORY}{IP_LIST_FILE_NAME}"

            if os.path.exists(ip_file_path):
                # Notepad megnyitása
                import subprocess
                subprocess.run(['notepad', ip_file_path], check=True)
                self.log_message(f"📄 IP lista megnyitva: {IP_LIST_FILE_NAME}", "SUCCESS")
            else:
                messagebox.showerror("Hiba", f"IP lista fájl nem található:\n{ip_file_path}")
                self.log_message(f"❌ IP lista fájl nem található: {ip_file_path}", "ERROR")

        except Exception as e:
            messagebox.showerror("Hiba", f"IP lista megnyitási hiba:\n{e}")
            self.log_message(f"❌ IP lista megnyitási hiba: {e}", "ERROR")

    # IP Kezelő metódusok
    def load_ip_data(self):
        """IP adatok betöltése"""
        try:
            # Táblázat törlése
            for item in self.ip_tree.get_children():
                self.ip_tree.delete(item)

            ip_file_path = f"{WORK_DIRECTORY}{IP_LIST_FILE_NAME}"

            if os.path.exists(ip_file_path):
                with open(ip_file_path, 'r', encoding='utf-8') as file:
                    for line_num, line in enumerate(file, 1):
                        parts = line.strip().split('\t')
                        if len(parts) >= 6:
                            device_name = parts[0]
                            ip_address = parts[1]
                            device_id = parts[2]
                            specialist = parts[3]
                            email = parts[4]
                            active = parts[5]

                            # Aktív státusz szöveggé alakítása
                            active_text = "✅ Igen" if active == "1" else "❌ Nem"

                            self.ip_tree.insert('', 'end', values=(
                                device_name, ip_address, device_id,
                                specialist, email, active_text
                            ))

                self.log_message(f"📋 IP lista betöltve: {line_num} eszköz", "SUCCESS")
            else:
                self.log_message(f"❌ IP lista fájl nem található: {ip_file_path}", "ERROR")

        except Exception as e:
            messagebox.showerror("Hiba", f"IP adatok betöltési hiba:\n{e}")
            self.log_message(f"❌ IP adatok betöltési hiba: {e}", "ERROR")

    def save_ip_data(self):
        """IP adatok mentése"""
        try:
            ip_file_path = f"{WORK_DIRECTORY}{IP_LIST_FILE_NAME}"

            with open(ip_file_path, 'w', encoding='utf-8') as file:
                for item in self.ip_tree.get_children():
                    values = self.ip_tree.item(item, 'values')
                    if len(values) >= 6:
                        device_name = values[0]
                        ip_address = values[1]
                        device_id = values[2]
                        specialist = values[3]
                        email = values[4]
                        active_text = values[5]

                        # Aktív státusz visszaalakítása
                        active = "1" if "✅" in active_text else "0"

                        line = f"{device_name}\t{ip_address}\t{device_id}\t{specialist}\t{email}\t{active}\n"
                        file.write(line)

            self.log_message("💾 IP lista mentve", "SUCCESS")
            messagebox.showinfo("Siker", "IP lista sikeresen mentve!")

        except Exception as e:
            messagebox.showerror("Hiba", f"IP adatok mentési hiba:\n{e}")
            self.log_message(f"❌ IP adatok mentési hiba: {e}", "ERROR")

    def filter_ip_list(self, event=None):
        """IP lista szűrése"""
        search_text = self.ip_search_var.get().lower()

        # Összes elem elrejtése
        for item in self.ip_tree.get_children():
            self.ip_tree.delete(item)

        # Szűrt elemek megjelenítése
        try:
            ip_file_path = f"{WORK_DIRECTORY}{IP_LIST_FILE_NAME}"

            if os.path.exists(ip_file_path):
                with open(ip_file_path, 'r', encoding='utf-8') as file:
                    for line in file:
                        parts = line.strip().split('\t')
                        if len(parts) >= 6:
                            device_name = parts[0].lower()
                            ip_address = parts[1].lower()

                            # Szűrés név vagy IP alapján
                            if not search_text or search_text in device_name or search_text in ip_address:
                                active_text = "✅ Igen" if parts[5] == "1" else "❌ Nem"

                                self.ip_tree.insert('', 'end', values=(
                                    parts[0], parts[1], parts[2],
                                    parts[3], parts[4], active_text
                                ))

        except Exception as e:
            self.log_message(f"❌ Szűrési hiba: {e}", "ERROR")

    def clear_ip_search(self):
        """Keresés törlése"""
        self.ip_search_var.set("")
        self.load_ip_data()

    def edit_selected_device(self, event=None):
        """Kiválasztott eszköz szerkesztése"""
        selection = self.ip_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.ip_tree.item(item, 'values')

        if len(values) >= 6:
            self.edit_device_name_var.set(values[0])
            self.edit_ip_var.set(values[1])
            self.edit_device_id_var.set(values[2])
            self.edit_specialist_var.set(values[3])
            self.edit_email_var.set(values[4])

            # Aktív státusz beállítása
            active_value = "1" if "✅" in values[5] else "0"
            self.edit_active_var.set(active_value)

            self.current_edit_item = item

    def save_device_changes(self):
        """Eszköz változások mentése"""
        if not hasattr(self, 'current_edit_item') or not self.current_edit_item:
            messagebox.showwarning("Figyelem", "Válasszon ki egy eszközt szerkesztéshez!")
            return

        try:
            device_name = self.edit_device_name_var.get().strip()
            ip_address = self.edit_ip_var.get().strip()
            device_id = self.edit_device_id_var.get().strip()
            specialist = self.edit_specialist_var.get().strip()
            email = self.edit_email_var.get().strip()
            active = self.edit_active_var.get()

            # Validáció
            if not device_name or not ip_address:
                messagebox.showerror("Hiba", "Eszköz név és IP cím kötelező!")
                return

            # Aktív státusz szöveggé alakítása
            active_text = "✅ Igen" if active == "1" else "❌ Nem"

            # Treeview frissítése
            self.ip_tree.item(self.current_edit_item, values=(
                device_name, ip_address, device_id,
                specialist, email, active_text
            ))

            self.log_message("✏️ Eszköz módosítva", "SUCCESS")
            messagebox.showinfo("Siker", "Eszköz sikeresen módosítva!")

        except Exception as e:
            messagebox.showerror("Hiba", f"Eszköz mentési hiba:\n{e}")
            self.log_message(f"❌ Eszköz mentési hiba: {e}", "ERROR")

    def add_new_device(self):
        """Új eszköz hozzáadása"""
        dialog = DeviceDialog(self.root, "Új eszköz hozzáadása")
        if dialog.result:
            device_data = dialog.device_data
            active_text = "✅ Igen" if device_data['active'] == "1" else "❌ Nem"

            self.ip_tree.insert('', 'end', values=(
                device_data['name'], device_data['ip'], device_data['id'],
                device_data['specialist'], device_data['email'], active_text
            ))

            self.log_message("➕ Új eszköz hozzáadva", "SUCCESS")

    def delete_selected_device(self):
        """Kiválasztott eszköz törlése"""
        selection = self.ip_tree.selection()
        if not selection:
            messagebox.showwarning("Figyelem", "Válasszon ki egy eszközt törléshez!")
            return

        if messagebox.askyesno("Megerősítés", "Biztosan törli a kiválasztott eszközt?"):
            for item in selection:
                self.ip_tree.delete(item)

            self.log_message("❌ Eszköz törölve", "SUCCESS")

    def clear_edit_fields(self):
        """Szerkesztő mezők törlése"""
        self.edit_device_name_var.set("")
        self.edit_ip_var.set("")
        self.edit_device_id_var.set("")
        self.edit_specialist_var.set("")
        self.edit_email_var.set("")
        self.edit_active_var.set("1")
        self.current_edit_item = None

    def reset_edit_fields(self):
        """Szerkesztő mezők visszaállítása"""
        if hasattr(self, 'current_edit_item') and self.current_edit_item:
            self.edit_selected_device()
        else:
            self.clear_edit_fields()

    def import_devices(self):
        """Eszközök importálása CSV fájlból"""
        try:
            from tkinter import filedialog

            file_path = filedialog.askopenfilename(
                title="CSV fájl kiválasztása",
                filetypes=[("CSV fájlok", "*.csv"), ("Minden fájl", "*.*")]
            )

            if file_path:
                import csv
                imported_count = 0

                with open(file_path, 'r', encoding='utf-8') as csvfile:
                    reader = csv.reader(csvfile, delimiter=';')

                    for row in reader:
                        if len(row) >= 5:
                            device_name = row[0].strip()
                            ip_address = row[1].strip()
                            device_id = row[2].strip() if len(row) > 2 else ""
                            specialist = row[3].strip() if len(row) > 3 else "Informatikus"
                            email = row[4].strip() if len(row) > 4 else ""
                            active = row[5].strip() if len(row) > 5 else "1"

                            if device_name and ip_address:
                                active_text = "✅ Igen" if active == "1" else "❌ Nem"

                                self.ip_tree.insert('', 'end', values=(
                                    device_name, ip_address, device_id,
                                    specialist, email, active_text
                                ))
                                imported_count += 1

                self.log_message(f"📋 {imported_count} eszköz importálva", "SUCCESS")
                messagebox.showinfo("Siker", f"{imported_count} eszköz sikeresen importálva!")

        except Exception as e:
            messagebox.showerror("Hiba", f"Import hiba:\n{e}")
            self.log_message(f"❌ Import hiba: {e}", "ERROR")


class DeviceDialog:
    """Eszköz hozzáadása/szerkesztése dialógus"""
    def __init__(self, parent, title="Eszköz szerkesztése"):
        self.result = False
        self.device_data = {}

        # Dialógus ablak
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Középre helyezés
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"400x300+{x}+{y}")

        self.setup_ui()

        # Várakozás a dialógus bezárására
        self.dialog.wait_window()

    def setup_ui(self):
        # Cím
        title_label = tk.Label(self.dialog, text="📝 Eszköz Adatok",
                              font=('Arial', 14, 'bold'))
        title_label.pack(pady=10)

        # Fő frame
        main_frame = tk.Frame(self.dialog)
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Mezők
        fields = [
            ("Eszköz név:", "name"),
            ("IP cím:", "ip"),
            ("Eszköz ID:", "id"),
            ("Informatikus:", "specialist"),
            ("Email:", "email")
        ]

        self.vars = {}

        for i, (label, key) in enumerate(fields):
            tk.Label(main_frame, text=label, font=('Arial', 10, 'bold')).grid(
                row=i, column=0, sticky='w', pady=5)

            self.vars[key] = tk.StringVar()
            if key == "specialist":
                self.vars[key].set("Informatikus")

            entry = tk.Entry(main_frame, textvariable=self.vars[key],
                           font=('Arial', 10), width=30)
            entry.grid(row=i, column=1, sticky='ew', padx=10, pady=5)

        # Aktív checkbox
        tk.Label(main_frame, text="Aktív:", font=('Arial', 10, 'bold')).grid(
            row=len(fields), column=0, sticky='w', pady=5)

        self.vars['active'] = tk.StringVar(value="1")
        active_combo = ttk.Combobox(main_frame, textvariable=self.vars['active'],
                                   values=["1", "0"], width=27, state='readonly')
        active_combo.grid(row=len(fields), column=1, sticky='ew', padx=10, pady=5)

        # Grid konfigurálás
        main_frame.grid_columnconfigure(1, weight=1)

        # Gombok
        button_frame = tk.Frame(main_frame)
        button_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)

        tk.Button(button_frame, text="💾 Mentés", command=self.save_clicked,
                 bg='#27ae60', fg='white', font=('Arial', 10, 'bold'), width=12).pack(side='left', padx=5)

        tk.Button(button_frame, text="❌ Mégse", command=self.cancel_clicked,
                 bg='#e74c3c', fg='white', font=('Arial', 10, 'bold'), width=12).pack(side='right', padx=5)

        # Enter és Escape kezelése
        self.dialog.bind('<Return>', lambda e: self.save_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())

    def save_clicked(self):
        name = self.vars['name'].get().strip()
        ip = self.vars['ip'].get().strip()

        if not name or not ip:
            messagebox.showerror("Hiba", "Eszköz név és IP cím kötelező!")
            return

        self.device_data = {
            'name': name,
            'ip': ip,
            'id': self.vars['id'].get().strip(),
            'specialist': self.vars['specialist'].get().strip(),
            'email': self.vars['email'].get().strip(),
            'active': self.vars['active'].get()
        }

        self.result = True
        self.dialog.destroy()

    def cancel_clicked(self):
        self.result = False
        self.dialog.destroy()


def main():
    root = tk.Tk()
    app = AdvancedSnmpGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
