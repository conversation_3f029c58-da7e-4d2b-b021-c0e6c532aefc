from global_parameters import WORK_DIRECTORY, INVENTORY_XLSX_FILE_NAME, IP_LIST_FILE_NAME, NEW_IP_LIST_FILE_NAME
import os, sys
import logging
import pandas as pd

logging.basicConfig(filename=WORK_DIRECTORY + 'snmp_app.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


class NewIpImport:
    def __init__(self):
        self.ip_list_file_name = f"{WORK_DIRECTORY}{IP_LIST_FILE_NAME}"
        self.ip_filter = '*'
        self.dict_inventory = []
        self.printers, self.new_printers = [], []
        self.has_change = False

    @staticmethod
    def _import_ip_list(txt_file_name, delimiter='\t', ip_filter='*', active_only=False, scanned_printers=None):
        if not os.path.isfile(f'{WORK_DIRECTORY}{txt_file_name}'):
            input(f'Nem találom az {txt_file_name} állományt a work könyvtárban!')
            sys.exit(0)
        with open(f'{WORK_DIRECTORY}{txt_file_name}', encoding='utf-8') as file:
            printers = [line.strip().split(delimiter) for line in file]
            if active_only:
                filtered_printers = []
                for i, printer in enumerate(printers):
                    if printer[5] == '1':  # Only the active printers
                        if ip_filter == printer[3] or ip_filter == '*':
                            if scanned_printers.count(
                                    [printer[2]]) == 0:  # Only get the printer if it wasn't already scanned
                                filtered_printers.append(printer)
                printers = filtered_printers
        return printers

    def import_ip_list(self):
        self.printers = self._import_ip_list(IP_LIST_FILE_NAME, '\t')
        self.new_printers = self._import_ip_list(NEW_IP_LIST_FILE_NAME, ' ')

    def import_inventory_xlsx(self):
        if not os.path.isfile(f'{WORK_DIRECTORY}{INVENTORY_XLSX_FILE_NAME}'):
            input(f'Nem találom a {INVENTORY_XLSX_FILE_NAME} állományt a work könyvtárban!')
            logging.error(f'Not found {WORK_DIRECTORY}{INVENTORY_XLSX_FILE_NAME}')
            sys.exit(0)
        try:
            df_inventory = pd.read_excel(f'{WORK_DIRECTORY}{INVENTORY_XLSX_FILE_NAME}', skiprows=1)
            self.dict_inventory = df_inventory.to_dict('records')
        except Exception as e:
            print(f'A {INVENTORY_XLSX_FILE_NAME} állomány beolvasása nem sikerült! {e}')
            logging.error(f'Failed to read {INVENTORY_XLSX_FILE_NAME}: {e}')

    def set_all_printers_passive(self):
        self.printers = [(ip, name, ea, site, department, '0') for ip, name, ea, site, department, _ in self.printers]

    @staticmethod
    def search_id(printers, searched_id):
        for index, printer in enumerate(printers):
            if printer[2] == searched_id:
                return index
        return -1

    def save_results(self):
        try:
            logging.info(f'File {IP_LIST_FILE_NAME} creating...')
            with open(f'{WORK_DIRECTORY}{IP_LIST_FILE_NAME}', "w", encoding='utf-8') as file:
                for printer in self.printers:
                    file.write("\t".join(item for item in printer) + "\n")
        except Exception as e:
            print(f'Az {IP_LIST_FILE_NAME} állomány elkészítése nem sikerült! {e}')
            logging.error(f'Failed to create file {IP_LIST_FILE_NAME}: {e}')

    def refresh_printers(self):
        self.import_inventory_xlsx()
        self.import_ip_list()
        for new_ip_printer in self.new_printers:
            row_number = self.search_id(self.printers, new_ip_printer[0])
            if row_number == -1:
                printer_name = [x['Megnevezés'] for x in self.dict_inventory if str(x['EA']) == str(new_ip_printer[0])]
                print(f'Not found {new_ip_printer[0]} value with IP: {new_ip_printer[1]} and name: {printer_name}.')
            else:
                self.printers[row_number][5] = new_ip_printer[2]  # '1'  # Active printer

                if new_ip_printer[1] != self.printers[row_number][1]:
                    print(f'ID: {self.printers[row_number][2]} {self.printers[row_number][0]} has new ip address '
                          f'{self.printers[row_number][1]} --> {new_ip_printer[1]}.')
                    reply = input('Is it changeable? (Y/N)')
                    if reply.lower() == 'y':
                        self.printers[row_number][1] = new_ip_printer[1]
                        self.has_change = True
        if self.has_change:
            self.save_results()
