#!/usr/bin/env python3
"""
Excel fájl generátor SNMP eredményekhez
"""

import os
from datetime import datetime
from global_parameters import WORK_DIRECTORY, YEAR_MONTH_DAY_TIME

def create_excel_from_snmp_data(answers_list, bad_answers_list, specialist="Informatikus"):
    """
    Excel fájl létrehozása SNMP szkennelési eredményekből (eredeti formátum)

    Args:
        answers_list: Sikeres SNMP válaszok listája
        bad_answers_list: Sikertelen SNMP válaszok listája
        specialist: Informatikus neve

    Returns:
        str: Létrehozott Excel fájl útvonala
    """
    try:
        import xlsxwriter
    except ImportError:
        raise ImportError("Az xlsxwriter csomag nincs telepítve! Telepítés: pip install xlsxwriter")

    # IP lista betöltése email címekhez
    def load_ip_list():
        ip_data = {}
        ip_file_path = f"{WORK_DIRECTORY}ip.txt"
        try:
            with open(ip_file_path, 'r', encoding='utf-8') as file:
                for line in file:
                    parts = line.strip().split('\t')
                    if len(parts) >= 5:
                        device_name = parts[0]
                        ip_address = parts[1]
                        device_id = parts[2]
                        email = parts[4]
                        key = f"{device_name}_{ip_address}_{device_id}"
                        ip_data[key] = email
        except Exception as e:
            print(f"IP lista betöltési hiba: {e}")
        return ip_data

    ip_emails = load_ip_list()

    # Excel fájl neve (eredeti formátum szóközökkel)
    excel_filename = f"Nyomtatóállások {YEAR_MONTH_DAY_TIME} - {specialist}.xlsx"
    excel_path = os.path.join(WORK_DIRECTORY, excel_filename)
    
    # Excel munkafüzet létrehozása
    workbook = xlsxwriter.Workbook(excel_path)
    
    # Formátumok
    header_format = workbook.add_format({
        'bold': True,
        'bg_color': '#4472C4',
        'font_color': 'white',
        'border': 1,
        'align': 'center'
    })

    success_format = workbook.add_format({
        'bg_color': '#C6EFCE',
        'border': 1
    })

    error_format = workbook.add_format({
        'bg_color': '#FFC7CE',
        'border': 1
    })

    normal_format = workbook.add_format({
        'border': 1
    })

    # Számformátum (ezres elválasztóval)
    number_format = workbook.add_format({
        'bg_color': '#C6EFCE',
        'border': 1,
        'num_format': '#,##0'
    })

    # Hivatkozás formátum
    link_format = workbook.add_format({
        'bg_color': '#C6EFCE',
        'border': 1,
        'font_color': 'blue',
        'underline': True
    })
    
    # 1. Nyomtatóállások munkalap (eredeti formátum)
    worksheet1 = workbook.add_worksheet('Nyomtatóállások')

    # Fejléc (eredeti oszlopok)
    from global_parameters import TODAY
    worksheet1.write(0, 0, f'BAVKH aktuális nyomtatóállások - {TODAY} - {specialist}')

    headers1 = ['Device name', 'Device ID', 'IP address', 'Total - Black pages', 'Color pages', 'Scanned pages', 'Other informations']
    for col, header in enumerate(headers1):
        worksheet1.write(1, col, header, header_format)
        
    # Sikeres válaszok adatai (eredeti formátum)
    if answers_list:
        # Eszközök csoportosítása IP cím szerint
        devices = {}
        for answer in answers_list:
            if len(answer) >= 5:
                device_name = answer[0]
                ip_address = answer[1]
                device_id = answer[2]
                oid = answer[3]
                value = answer[4]

                # Eszköz kulcs
                device_key = f"{device_name}_{ip_address}_{device_id}"

                if device_key not in devices:
                    devices[device_key] = {
                        'name': device_name,
                        'id': device_id,
                        'ip': ip_address,
                        'total_black': '',
                        'color': '',
                        'scanned': '',
                        'other': [],
                        # Xerox specifikus mezők
                        'xerox_total_impressions': '',
                        'xerox_black_impressions': '',
                        'xerox_color_200': '',
                        'xerox_color_201': ''
                    }

                # OID alapján értékek besorolása
                if oid == '*******.*********.*******.1':  # Standard Total Black pages
                    devices[device_key]['total_black'] = value
                elif oid == '*******.*********.*********.**********':  # Xerox Black impressions
                    devices[device_key]['xerox_black_impressions'] = value
                elif oid == '*******.*********.*********.**********':  # Xerox Total impressions
                    devices[device_key]['xerox_total_impressions'] = value
                elif oid in ['*******.4.1.18334.*******.*******.0',      # Konica Minolta scanned
                           '*******.4.1.367.*******.********.25',        # Ricoh scanned
                           '*******.*********.*********.***********']:   # Xerox scanned
                    devices[device_key]['scanned'] = value
                elif oid != '*******.*******.0':  # Nem hostname
                    devices[device_key]['other'].append(f"{oid}: {value}")

        # Xerox színes oldalak számítása
        for device_key, device in devices.items():
            # Xerox színes oldalak = Total impressions - Black impressions
            if (device['xerox_total_impressions'] and device['xerox_black_impressions'] and
                device['xerox_total_impressions'].isdigit() and device['xerox_black_impressions'].isdigit()):

                total_impressions = int(device['xerox_total_impressions'])
                black_impressions = int(device['xerox_black_impressions'])
                color_impressions = total_impressions - black_impressions

                # Xerox adatok beállítása
                device['total_black'] = str(black_impressions)
                device['color'] = str(color_impressions) if color_impressions > 0 else ''

        # Eszközök írása az Excel-be
        row = 2
        for device_key, device in devices.items():
            # Email cím lekérése
            email = ip_emails.get(device_key, '')

            # Device name
            worksheet1.write(row, 0, device['name'], success_format)

            # Device ID
            worksheet1.write(row, 1, device['id'], success_format)

            # IP address (hivatkozásként)
            ip_address = device['ip']
            if ip_address:
                worksheet1.write_url(row, 2, f"http://{ip_address}", link_format, ip_address)
            else:
                worksheet1.write(row, 2, ip_address, success_format)

            # Total - Black pages (számként)
            if device['total_black'] and device['total_black'].isdigit():
                worksheet1.write_number(row, 3, int(device['total_black']), number_format)
            else:
                worksheet1.write(row, 3, device['total_black'], success_format)

            # Color pages (számként)
            if device['color'] and device['color'].isdigit():
                worksheet1.write_number(row, 4, int(device['color']), number_format)
            else:
                worksheet1.write(row, 4, device['color'], success_format)

            # Scanned pages (számként)
            if device['scanned'] and device['scanned'].isdigit():
                worksheet1.write_number(row, 5, int(device['scanned']), number_format)
            else:
                worksheet1.write(row, 5, device['scanned'], success_format)

            # Other informations (email cím)
            worksheet1.write(row, 6, email, success_format)

            row += 1

    # Oszlopok automatikus méretezése
    worksheet1.autofit()
    
    # 2. Nem elérhető eszközök munkalap (eredeti formátum)
    worksheet2 = workbook.add_worksheet('Nem elérhető eszközök')

    # Fejléc
    worksheet2.write(0, 0, f'BAVKH nem elérhető eszközök - {TODAY} - {specialist}')

    headers2 = ['Device name', 'Device ID', 'IP address', 'Informatikus', 'Email']
    for col, header in enumerate(headers2):
        worksheet2.write(1, col, header, header_format)

    # Sikertelen válaszok adatai
    if bad_answers_list:
        for row, bad_answer in enumerate(bad_answers_list, 2):  # 2-től kezdjük
            row_data = [
                bad_answer[0] if len(bad_answer) > 0 else '',  # Device name
                bad_answer[2] if len(bad_answer) > 2 else '',  # Device ID
                bad_answer[1] if len(bad_answer) > 1 else '',  # IP address
                bad_answer[3] if len(bad_answer) > 3 else specialist,  # Informatikus
                bad_answer[4] if len(bad_answer) > 4 else ''   # Email
            ]

            for col, value in enumerate(row_data):
                worksheet2.write(row, col, str(value), error_format)

    # Oszlopok automatikus méretezése
    worksheet2.autofit()
    
    # Oszlopok szélessége beállítása
    worksheet1.set_column('A:A', 25)  # Device name
    worksheet1.set_column('B:B', 15)  # Device ID
    worksheet1.set_column('C:C', 15)  # IP address
    worksheet1.set_column('D:D', 18)  # Total - Black pages
    worksheet1.set_column('E:E', 15)  # Color pages
    worksheet1.set_column('F:F', 15)  # Scanned pages
    worksheet1.set_column('G:G', 30)  # Other informations

    worksheet2.set_column('A:A', 25)  # Device name
    worksheet2.set_column('B:B', 15)  # Device ID
    worksheet2.set_column('C:C', 15)  # IP address
    worksheet2.set_column('D:D', 15)  # Informatikus
    worksheet2.set_column('E:E', 25)  # Email
    
    # Munkafüzet bezárása
    workbook.close()
    
    return excel_path

def create_simple_excel_from_results(results_data, specialist="Informatikus"):
    """
    Egyszerű Excel fájl létrehozása GUI eredményekből
    
    Args:
        results_data: GUI eredmények listája (device_name, ip, id, status, response, timestamp)
        specialist: Informatikus neve
    
    Returns:
        str: Létrehozott Excel fájl útvonala
    """
    try:
        import xlsxwriter
    except ImportError:
        raise ImportError("Az xlsxwriter csomag nincs telepítve! Telepítés: pip install xlsxwriter")
    
    # Excel fájl neve
    excel_filename = f"SNMP-Eredmények-{YEAR_MONTH_DAY_TIME}-{specialist}.xlsx"
    excel_path = os.path.join(WORK_DIRECTORY, excel_filename)
    
    # Excel munkafüzet létrehozása
    workbook = xlsxwriter.Workbook(excel_path)
    worksheet = workbook.add_worksheet('SNMP Eredmények')
    
    # Formátumok
    header_format = workbook.add_format({
        'bold': True,
        'bg_color': '#4472C4',
        'font_color': 'white',
        'border': 1,
        'align': 'center'
    })
    
    success_format = workbook.add_format({
        'bg_color': '#C6EFCE',
        'border': 1
    })
    
    error_format = workbook.add_format({
        'bg_color': '#FFC7CE',
        'border': 1
    })
    
    # Fejléc
    headers = ['Eszköz név', 'IP cím', 'ID', 'Állapot', 'Válasz', 'Szkennelés időpontja']
    for col, header in enumerate(headers):
        worksheet.write(0, col, header, header_format)
    
    # Adatok
    for row, result in enumerate(results_data, 1):
        for col, value in enumerate(result):
            # Formátum kiválasztása állapot alapján
            if col == 3:  # Állapot oszlop
                if "Sikeres" in str(value):
                    format_to_use = success_format
                else:
                    format_to_use = error_format
            else:
                format_to_use = success_format if "Sikeres" in str(result[3]) else error_format
            
            worksheet.write(row, col, str(value), format_to_use)
    
    # Oszlopok automatikus méretezése
    worksheet.autofit()
    
    # Munkafüzet bezárása
    workbook.close()
    
    return excel_path

if __name__ == "__main__":
    # Teszt
    test_answers = [
        ["Test Device 1", "*************", "DEV001", "*******.2.1.1.1.0", "HP LaserJet"],
        ["Test Device 2", "192.168.1.101", "DEV002", "*******.2.1.1.1.0", "Canon Printer"]
    ]
    
    test_bad_answers = [
        ["Bad Device 1", "192.168.1.200", "DEV003", "Informatikus", "<EMAIL>"],
    ]
    
    excel_file = create_excel_from_snmp_data(test_answers, test_bad_answers, "Test")
    print(f"Excel fájl létrehozva: {excel_file}")
