#!/usr/bin/env python3
"""
SNMP Scanner GUI Starter
Beállítja a Tcl/Tk környezeti változókat és elindítja a GUI-t
"""

import os
import sys
import subprocess

def setup_tcl_tk():
    """Tcl/Tk környezeti változók beállítása"""
    python_dir = os.path.dirname(sys.executable)
    tcl_path = os.path.join(python_dir, "tcl", "tcl8.6")
    tk_path = os.path.join(python_dir, "tcl", "tk8.6")
    
    print(f"Python könyvtár: {python_dir}")
    print(f"TCL útvonal: {tcl_path}")
    print(f"TK útvonal: {tk_path}")
    
    if os.path.exists(tcl_path) and os.path.exists(tk_path):
        os.environ['TCL_LIBRARY'] = tcl_path
        os.environ['TK_LIBRARY'] = tk_path
        print("✅ Tcl/Tk környezeti változók beállítva")
        return True
    else:
        print("❌ Tcl/Tk útvonalak nem találhatók")
        return False

def test_tkinter():
    """Tkinter teszt"""
    try:
        import tkinter as tk
        print("✅ Tkinter import sikeres")
        return True
    except Exception as e:
        print(f"❌ Tkinter import hiba: {e}")
        return False

def show_menu():
    """Menü megjelenítése"""
    print("\n" + "="*50)
    print("🖨️  SNMP Nyomtató Scanner - GUI Indító")
    print("="*50)
    print("1. Minimális GUI (ajánlott)")
    print("2. Egyszerű GUI")
    print("3. Fejlett GUI")
    print("4. Launcher")
    print("5. Konzol verzió")
    print("6. Tkinter teszt")
    print("0. Kilépés")
    print("="*50)

def run_gui(choice):
    """GUI indítása"""
    gui_files = {
        '1': 'snmp_gui_minimal.py',
        '2': 'snmp_gui_simple.py', 
        '3': 'snmp_gui_advanced.py',
        '4': 'launcher.py',
        '5': 'snmp.py'
    }
    
    if choice == '6':
        # Tkinter teszt
        try:
            import tkinter as tk
            root = tk.Tk()
            root.title("Tkinter Teszt")
            root.geometry("300x200")
            
            tk.Label(root, text="✅ Tkinter működik!", 
                    font=('Arial', 14), fg='green').pack(pady=20)
            tk.Label(root, text="Bezárhatja ezt az ablakot.", 
                    font=('Arial', 10)).pack(pady=10)
            tk.Button(root, text="Bezárás", 
                     command=root.destroy, bg='red', fg='white').pack(pady=10)
            
            print("✅ Tkinter teszt ablak megnyitva")
            root.mainloop()
            return True
        except Exception as e:
            print(f"❌ Tkinter teszt hiba: {e}")
            return False
    
    if choice not in gui_files:
        print("❌ Érvénytelen választás!")
        return False
    
    gui_file = gui_files[choice]
    
    if not os.path.exists(gui_file):
        print(f"❌ Fájl nem található: {gui_file}")
        return False
    
    try:
        print(f"🚀 {gui_file} indítása...")
        
        if choice == '5':
            # Konzol verzió speciális kezelése
            subprocess.run([sys.executable, gui_file, 'run'], check=True)
        else:
            # GUI verziók
            subprocess.run([sys.executable, gui_file], check=True)
        
        print("✅ Program sikeresen lefutott")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Program futtatási hiba: {e}")
        return False
    except FileNotFoundError:
        print(f"❌ Python nem található: {sys.executable}")
        return False
    except Exception as e:
        print(f"❌ Váratlan hiba: {e}")
        return False

def main():
    """Főprogram"""
    print("SNMP Scanner GUI Starter")
    print("Tcl/Tk környezet ellenőrzése...")
    
    # Tcl/Tk beállítása
    if not setup_tcl_tk():
        print("\n⚠️  Figyelem: Tcl/Tk problémák lehetnek!")
        print("Próbálja meg a Tkinter tesztet (6. opció)")
    
    # Tkinter teszt
    if not test_tkinter():
        print("\n❌ Tkinter nem működik!")
        print("Lehetséges megoldások:")
        print("1. Python újratelepítése tkinter-rel")
        print("2. Tcl/Tk manuális telepítése")
        input("\nNyomjon Enter-t a folytatáshoz...")
    
    # Főciklus
    while True:
        show_menu()
        
        try:
            choice = input("\nVálasztás (0-6): ").strip()
            
            if choice == '0':
                print("👋 Viszlát!")
                break
            
            if choice in ['1', '2', '3', '4', '5', '6']:
                print(f"\n🔄 Választás: {choice}")
                
                if run_gui(choice):
                    print("✅ Művelet befejezve")
                else:
                    print("❌ Művelet sikertelen")
                
                input("\nNyomjon Enter-t a folytatáshoz...")
            else:
                print("❌ Érvénytelen választás! (0-6)")
                
        except KeyboardInterrupt:
            print("\n\n👋 Kilépés...")
            break
        except Exception as e:
            print(f"\n❌ Hiba: {e}")
            input("Nyomjon Enter-t a folytatáshoz...")

if __name__ == "__main__":
    main()
