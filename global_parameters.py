import datetime
import os
import re

# Constants
WORK_DIRECTORY = os.getenv('WORK_DIRECTORY', 'work/')  # directory for work files
INVENTORY_XLSX_FILE_NAME = "leltar-bavkh.xlsx"
IP_LIST_FILE_NAME = "ip.txt"
NEW_IP_LIST_FILE_NAME = "new_ip.txt"
SCANNED_PRINTER = "scanned_printer.txt"
TODAY = datetime.date.today()
YEAR_MONTH_DAY_TIME = datetime.datetime.now().strftime("%Y.%m.%d - %H.%M")
elapsed_time = 2

# SMTP constants
PORT = int(os.getenv('SMTP_PORT', 587))
SMTP_SERVER = os.getenv('SMTP_SERVER', '*************')
LOGIN_NAME = os.getenv('SMTP_LOGIN', 'printer')
PASSWORD = os.getenv('SMTP_PASSWORD', 'Baranya123')

SPINNER = ['-', '\\', '|', '/', '-', '\\', '|', '/']

COMMON_OIDS = {
    "device_name": "*******.*******.0",
    "total_pages": "*******.*********.*******.1",
}

PRINTER_MODELS = {
    "Konica Minolta Bizhub C+": {
        "oids": {
            **COMMON_OIDS,
            "black_copy": "*******.4.1.18334.*******.*******.5.1.1",  # OID for black copy
            "black_print": "*******.4.1.18334.*******.*******.5.1.2",  # OID for black print
            "color_copy": "*******.4.1.18334.*******.*******.5.2.1",  # OID for color copy
            "color_print": "*******.4.1.18334.*******.*******.5.2.2",  # OID for color print
            "2_color_cp": "*******.4.1.18334.*******.*******.5.4.2",  # OID for 2-color copy and print
            "scanned_pages": "*******.4.1.18334.*******.7.2.1.5.0",  # OID for scanned pages
        },
    },
    "Konica Minolta Bizhub+": {  # Catch-all for other Konica Minolta
        "oids": {
            **COMMON_OIDS,
            "scanned_pages": "*******.4.1.18334.*******.7.2.1.5.0",  # OID for scanned pages
        },
    },
    "HP LaserJet Pro 200 color M251n+|HP Color LaserJet M553dn+|HP Color LaserJet MFP E78325+": {
        "oids": {
            **COMMON_OIDS,
            "black_imp": "*******.4.1.11.2.3.9.4.*******.2.6.0",  # OID for black impressions
            "color_imp": "*******.4.1.11.2.3.9.4.*******.2.7.0",  # OID for color impressions
        },
    },
    "HP LaserJet M2727nf MFP+": {
        "oids": {
            **COMMON_OIDS,
        },
    },
    "Xerox Versalink B+": {
        "oids": {
            **COMMON_OIDS,
            "a4_equal_pages": "*******.4.1.253.8.53.13.2.1.6.1.20.201",  # OID for A4 equal pages
            "scanned_pages": "*******.4.1.253.8.53.13.2.1.6.102.20.21",  # OID for scanned pages
        },
    },
    "Xerox Versalink C+": {
        "oids": {
            **COMMON_OIDS,
            "a4_equal_pages": "*******.4.1.253.8.53.13.2.1.6.1.20.200",  # OID for A4 equal p
            "a4_equal_black_pages": "*******.4.1.253.8.53.13.2.1.6.1.20.201",  # OID for A4 equal black pages
            "a4_equal_color_pages": "*******.4.1.253.8.53.13.2.1.6.1.20.202",  # OID for A4 equal color pages
            "scanned_pages": "*******.4.1.253.8.53.13.2.1.6.102.20.21",  # OID for scanned pages
        },
    },
    "Xerox WorkCentre 5325+": {
        "oids": {
            **COMMON_OIDS,
            "scanned_pages": "*******.4.1.253.8.53.13.2.1.6.102.20.21",  # OID for scanned pages
        },
    },
    "Ricoh MP2501SP|Ricoh SP4510SF": {
        "oids": {
            **COMMON_OIDS,
            "color_scanned_pages": "*******.4.1.367.3.2.1.2.19.5.1.9.24",  # OID for color scanned pages
            "black_scanned_pages": "*******.4.1.367.3.2.1.2.19.5.1.9.25",  # OID for black scanned pages
        },
    },
}

def get_printer_oids(printer_name):
    """
    A függvény visszaadja a megfelelő OID listát egy adott nyomtatónévhez.

    :param printer_name: A nyomtató neve (str)
    :return: A nyomtatóhoz tartozó OID lista (list) vagy None, ha nincs találat.
    """
    for pattern, data in PRINTER_MODELS.items():
        if re.match(pattern, printer_name):
            return data["oids"]
    return COMMON_OIDS
