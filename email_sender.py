import smtplib
from global_parameters import WORK_DIRECTORY, YEAR_MONTH_DAY_TIME, PORT, SMTP_SERVER, LOGIN_NAME, PASSWORD, TODAY
from email import encoders
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from os.path import basename
import logging


def send_email(receiver: object, receiver_email: str, year_month_for_sending: object,
                          it_specialist: object) -> bool:
    sender_email = "<EMAIL>"
    message = MIMEMultipart("alternative")
    message["Subject"] = f"Havi nyomtatóállások: {YEAR_MONTH_DAY_TIME} - {it_specialist}"
    message["From"] = 'Kékkő<PERSON>'
    message["To"] = receiver_email

    # write the text/plain part
    logging.info(f'Sending email to {receiver_email}')
    text = f"""\
    Kedves {receiver}!

    Mellékelten küldöm a {TODAY} napon lekérdezett nyomtatóállásokat további szíves feldolgozásra!

    """

    # write the HTML part
    html = f"""\
    <html>
      <body>
        <p>Kedves {receiver}!<br>

           Mellékelten küldöm a {TODAY} napon lekérdezett nyomtatóállásokat további szíves feldolgozásra!</p>
      </body>
    </html>
    """

    part1 = MIMEText(text, "plain")
    message.attach(part1)

    # Open XLSX file in binary mode
    filename = f'{WORK_DIRECTORY}Nyomtatóállások {YEAR_MONTH_DAY_TIME} - {it_specialist}.xlsx'
    with open(filename, 'rb') as file:
        part3 = MIMEApplication(file.read(), Name=basename(filename))

    # Encode file in ASCII characters to send by email
    encoders.encode_base64(part3)

    # Open SQL file in binary mode
    filename = f'{WORK_DIRECTORY}sql{year_month_for_sending}.txt'
    with open(filename, 'rb') as file:
        part4 = MIMEApplication(file.read(), Name=basename(filename))

    # Encode file in ASCII characters to send by email
    encoders.encode_base64(part4)

    # Add header as key/value pair to attachment part
    part3['Content-Disposition'] = f'attachment; filename="Nyomtatóállások {TODAY} - {it_specialist}.xlsx"'
    part4['Content-Disposition'] = f'attachment; filename="sql{year_month_for_sending}.txt"'

    # Add attachment to message and convert message to string
    message.attach(part3)
    message.attach(part4)

    with smtplib.SMTP(SMTP_SERVER, PORT) as server:
        server.ehlo()
        server.starttls()
        server.ehlo()
        server.login(LOGIN_NAME, PASSWORD)
        server.sendmail(sender_email, receiver_email, message.as_string())
        server.quit()

    print(f'{receiver} részére a {receiver_email} címre a levél elküldve.')
    logging.info(f'Email sent to {receiver_email}')
    return True
