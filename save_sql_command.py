import datetime, time
from global_parameters import WORK_DIRECTORY, TODAY, YEAR_MONTH_DAY_TIME

from database import sql_connect

def run():
    conn = sql_connect()
    cursor = conn.cursor

    # SQL processing
    conn.execute('SELECT * FROM printers ORDER BY device_id, timestamp')

    rows = cursor.fetchall()
    device_id = ''
    col = 0
    black_page, color_page, scan_page = 0, 0, 0
    sql_command = ''
    year_month = f'{str(TODAY)[0:4]}{str(TODAY)[5:7]}'

    for row in rows:
        if device_id != row[0]:
            device_id = row[0]
            col = 0
        col += 1
        if col == 1:
            # A szöveg felosztása a "-" karakter alapján
            date_str, time_str = row[1].split(" - ")

            # Dátum és idő rész alakítása
            year, month, day = map(int, date_str.split("."))
            hour, minute = map(int, time_str.split("."))

            # datetime objektum létrehozása
            date_time_obj = datetime.datetime(year, month, day, hour, minute)

            sql_command += f'DELETE FROM torzsadattar.leltar_nyomtatoallas_rogzites' \
                           f' WHERE LNyA_LAzonosito = {device_id} AND LNyA_Honap = "{year_month}";\n'
            sql_command += 'INSERT INTO torzsadattar.leltar_nyomtatoallas_rogzites ' \
                           '(LNyA_LAzonosito, LNyA_Szamlalo_Ff, LNyA_Szamlalo_Co, LNyA_Honap, LNyA_Rogzito,' \
                           ' LNyA_Datum, LNyA_Megjegyzes, LNyA_Szamlalo_Scan) VALUES ('
            sql_command += f'{device_id}, '
            sql_command += f'{row[6]}, {row[7]}, "{year_month}",' \
                           f' "2290", {(time.mktime(date_time_obj.timetuple()))}, "Gépi beolvasás", ' \
                           f'{row[8]});\n'
    print(sql_command)
    try:
        with open(f'{WORK_DIRECTORY}sql{YEAR_MONTH_DAY_TIME}.txt', 'a', encoding='utf-8') as file:
            file.writelines(sql_command)
    except Exception as e:
        print(f'A {WORK_DIRECTORY}sql{YEAR_MONTH_DAY_TIME}.txt állomány elkészítése nem sikerült! {e}')
