# 🚀 SNMP Scanner - Gyors Indítás

## ⚡ Azonnali <PERSON>

```bash
py start_gui.py
```

**Válassza ki a kívánt opciót:**
- `1` - **Minimális GUI** (a<PERSON><PERSON><PERSON><PERSON> k<PERSON>knek)
- `2` - **Egyszerű GUI** (több funkcióval)  
- `3` - **Fejlett GUI** (teljes funkcionalitás)
- `4` - **Launcher** (verz<PERSON><PERSON>ztó)
- `5` - **Konzol verzió**
- `6` - **Tkinter teszt**

## 🎯 Ajánlott Használat

### Kezdőknek: Minimális GUI (1)
- Egyszerű kezelőfelület
- Alapvető funkciók
- Stabil működés

### Haladóknak: Fejlett GUI (3)  
- Többfüles felület
- Részletes beállítások
- Exportálási lehetőségek

## 📋 Gyo<PERSON> Szkennelés Lépései

1. **Indítás:** `py start_gui.py`
2. **Választás:** `1` (Minimális GUI)
3. **Beállítás:** Informatikus szűrő
4. **Indítás:** "🚀 Start" gomb
5. **Várakozás:** Eredmények megjelenése
6. **Eredmények:** `work/` mappában

## 🔧 Hibaelhárítás

### "Tcl/Tk hiba"
✅ **Megoldva!** A `start_gui.py` automatikusan javítja

### "GUI nem jelenik meg"
- Ellenőrizze a taskbar-t
- Alt+Tab az ablakok között
- Próbálja a Tkinter tesztet (6)

### "IP lista nem található"
- Ellenőrizze: `work/ip.txt` létezik
- Formátum: TAB elválasztott mezők
- Utolsó mező: `1` (aktív eszközök)

## 📁 Eredmények Helye

```
work/
├── answers-YYYY.MM.DD-HH.MM-Informatikus.txt    # Sikeres válaszok
├── bad_answers-YYYY.MM.DD-HH.MM-Informatikus.txt # Hibás válaszok  
├── ip.txt                                        # IP lista
└── *.xlsx                                        # Excel fájlok
```

## 🎨 GUI Verziók Összehasonlítása

| Funkció | Minimális | Egyszerű | Fejlett |
|---------|-----------|----------|---------|
| SNMP szkennelés | ✅ | ✅ | ✅ |
| Valós idejű statisztikák | ✅ | ✅ | ✅ |
| Színes log | ✅ | ✅ | ✅ |
| Beállítások mentése | ❌ | ❌ | ✅ |
| Teszt funkciók | ✅ | ❌ | ✅ |
| Többfüles felület | ❌ | ❌ | ✅ |
| Tanulási görbe | Könnyű | Könnyű | Közepes |

## 💡 Tippek

- **Első használat:** Próbálja a Tkinter tesztet (6)
- **Gyors szkennelés:** Minimális GUI (1)
- **Teljes funkciók:** Fejlett GUI (3)
- **Debug mód:** Részletes hibaüzenetek
- **Work mappa:** Gyors hozzáférés minden GUI-ban

## 🆘 Támogatás

- **Dokumentáció:** `GUI_README.md`
- **Részletes súgó:** Fejlett GUI menüjében
- **Hibák:** Ellenőrizze a log üzeneteket

---

**Verzió:** 2.0  
**Utolsó frissítés:** 2025-08-08  
**Tcl/Tk fix:** ✅ Beépítve minden GUI-ba
