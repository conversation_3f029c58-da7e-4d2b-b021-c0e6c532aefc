import sys
import asyncio
from pysnmp import debug
from pysnmp.carrier.asyncio.dgram import udp
from pysnmp.proto import api
from pysnmp.carrier.asyncio.dispatch import AsyncioDispatcher
from pyasn1.codec.ber import encoder, decoder

from global_parameters import get_printer_oids, SCANNED_PRINTER, IP_LIST_FILE_NAME
from new_ip_import import NewIpImport

# debug.set_logger(debug.Debug('all'))

class SnmpGetter:
    def __init__(self):
        self.printers = []
        self.answers_from_printer = []
        self.bad_answers_from_printer = []

    @staticmethod
    def open_ip_list():
        new_ip_import = NewIpImport()
        scanned_printers = new_ip_import._import_ip_list(SCANNED_PRINTER, '\n')
        printers = new_ip_import._import_ip_list(IP_LIST_FILE_NAME, '\t', '*', True, scanned_printers)

        if not printers:
            print('üres IP állomány!')
            sys.exit(0)

        print(f'{len(printers)} darab eszköz IP címe beolvasva.')
        print('A feldolgozást megkezdem.')
        return printers

    @staticmethod
    def get_error_message(error_code):
        error_messages = {
            0: "Sikeres válasz.", 1: "Túl nagy a válasz.", 2: "Nem létező OID.",
            3: "Érvénytelen érték.", 4: "Írásvédett változó.", 5: "Általános hiba.",
            6: "Hozzáférési jogok hiánya.", 7: "Hibás adattípus.", 8: "Helytelen adat hossz.",
            9: "Helytelen kódolás.", 10: "Helytelen érték.", 11: "Nem lehet létrehozni.",
            12: "Inkonzisztens érték.", 13: "Erőforrás nem elérhető.",
            14: "A művelet elkötelezése nem sikerült.", 15: "A visszavonás nem sikerült.",
            16: "Engedélyezési hiba.", 17: "Nem írható objektum.", 18: "Inkonzisztens név."
        }
        return error_messages.get(error_code, "Ismeretlen hiba.")

    async def scan_printer(self, printer):
        pMod = api.PROTOCOL_MODULES[api.SNMP_VERSION_2C]
        reqPDU = pMod.GetRequestPDU()
        pMod.apiPDU.set_defaults(reqPDU)
        reqMsg = pMod.Message()
        pMod.apiMessage.set_defaults(reqMsg)
        pMod.apiMessage.set_community(reqMsg, "public")
        pMod.apiMessage.set_pdu(reqMsg, reqPDU)

        future = asyncio.Future()

        def cbrecvfun(dispatcher, transportDomain, transportAddress, wholeMsg, reqPDU=reqPDU):
            if future.done():
                return

            while wholeMsg:
                rspMsg, wholeMsg = decoder.decode(wholeMsg, asn1Spec=pMod.Message())
                rspPDU = pMod.apiMessage.get_pdu(rspMsg)

                if pMod.apiPDU.get_request_id(reqPDU) == pMod.apiPDU.get_request_id(rspPDU):
                    errorStatus = pMod.apiPDU.get_error_status(rspPDU)

                    if errorStatus != 0:
                        self.bad_answers_from_printer.append(
                            [printer[0], printer[1], printer[2], printer[3], printer[4]])
                        future.set_result(False)
                    else:
                        for oid, val in pMod.apiPDU.get_varbinds(rspPDU):
                            self.answers_from_printer.append(
                                [printer[0], printer[1], printer[2], oid.prettyPrint(), val.prettyPrint()])
                        future.set_result(True)

        dispatcher = AsyncioDispatcher()
        dispatcher.register_recv_callback(cbrecvfun)
        dispatcher.register_transport(udp.domainName, udp.UdpAsyncioTransport().open_client_mode())

        oid_list = [(oid, pMod.Null('')) for oid in get_printer_oids(printer[0]).values()]
        pMod.apiPDU.set_varbinds(reqPDU, oid_list)

        dispatcher.send_message(encoder.encode(reqMsg), udp.domainName, udp.UdpTransportAddress((printer[1], 161)))
        dispatcher.job_started(1)

        try:
            result = await asyncio.wait_for(future, timeout=5.05)
            return printer[1], "Sikeres SNMP válasz" if result else "SNMP válasz hiba"
        except asyncio.TimeoutError:
            # print(f"Időtúllépés: {printer[0]}")
            self.bad_answers_from_printer.append([printer[0], printer[1], printer[2], printer[3], printer[4]])
            return printer[1], "SNMP időtúllépés"
        finally:
            dispatcher.close_dispatcher()

    async def run(self):
        self.printers = self.open_ip_list()
        tasks = [self.scan_printer(printer) for printer in self.printers]
        await asyncio.gather(*tasks, return_exceptions=True)
