import asyncio
import datetime
import glob
import logging
import os
import sys
import time

import analyse_data
import database
import save_printer_oid_answers
import save_sql_command
from email_sender import send_email
import pyfiglet.fonts
import xlsxwriter
from colorama import Fore, Style, init as colorama_init
from snmp_getter import SnmpGetter

from new_ip_import import NewIpImport
from global_parameters import WORK_DIRECTORY, TODAY, YEAR_MONTH_DAY_TIME, SPINNER


analyzing = False  # mode of working

logging.basicConfig(filename=WORK_DIRECTORY + 'snmp_app.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


class PrinterScanner:
    def __init__(self, ip_list_file_name, output_file, ip_filter='*'):
        self.ip_list_file_name = ip_list_file_name
        self.ip_filter = ip_filter
        self.printers = []
        self.output_file = output_file
        self.printer_number = len(self.printers)
        self.progress = 0
        self.workbook = xlsxwriter.Workbook(output_file)
        self.worksheet1, self.worksheet2 = self._add_worksheets_header()
        self.year_month = f'{str(TODAY)[0:4]}{str(TODAY)[5:7]}'
        self.sql_command = ''
        self.colorama_init()
        self.timestamp = datetime.datetime.now()
        self.results = []
        self.elapsed_time = .1
        self.has_answer = bool

    def save_sql(self):
        try:
            with open(f'{WORK_DIRECTORY}sql{YEAR_MONTH_DAY_TIME}.txt', 'a', encoding='utf-8') as file:
                file.writelines(self.sql_command)
        except Exception as e:
            print(f'A {WORK_DIRECTORY}sql{YEAR_MONTH_DAY_TIME}.txt állomány elkészítése nem sikerült! {e}')

    @staticmethod
    def colorama_init():
        colorama_init()

    def _add_worksheets_header(self):
        bold = self.workbook.add_format({'bold': True})  # Add a bold format to use to highlight cells.

        worksheet1 = self.workbook.add_worksheet('Nyomtatóállások')
        headers1 = ['Device name', 'Device ID', 'IP address', 'Total - Black pages', 'Color pages', 'Scanned pages',
                    'Other informations']
        worksheet1.write(0, 0, f'BAVKH aktuális nyomtatóállások - {TODAY}-{it_specialist}')
        worksheet1.write_row(1, 0, headers1, bold)

        worksheet2 = self.workbook.add_worksheet('Nem elérhető eszközök')
        headers2 = ['Device name', 'Device ID', 'IP address']
        worksheet2.write(0, 0, f'BAVKH aktuális nyomtatóállások (nem elérhető eszközök) - {TODAY}-{it_specialist}')
        worksheet2.write_row(1, 0, headers2, bold)

        return worksheet1, worksheet2

    @staticmethod
    def _update_progress(progress, comment='', bar_length=30):
        status = ""
        if isinstance(progress, int):
            progress = float(progress)
        if not isinstance(progress, float):
            progress = 0
            status = "Hiba: progress var must be float\r\n"
        if progress < 0:
            progress = 0
            status = "Állj...\r\n"
        if progress >= 1:
            progress = 1
            status = "Kész...\r\n"
            comment = ''

        block = int(round(bar_length * progress))
        spinner_char = SPINNER[int(progress * 100) % 8]
        progress_percentage = round(progress * 100, 2)

        text = f"{Fore.CYAN}\rFolyamat: [{spinner_char}] [{'=' * block + ' ' * (bar_length - block)}] " \
               f"{Fore.LIGHTYELLOW_EX}{progress_percentage}% {status} {Fore.GREEN}{comment}{Style.RESET_ALL}"

        sys.stdout.write(text)
        sys.stdout.flush()

    @staticmethod
    def process_printer(worksheet, answers, i, xlsx_row):
        """Feldolgozza a nyomtatók válaszait és kiírja az Excel táblába."""
        printer_model = answers[i][0]
        worksheet.write(xlsx_row, 0, printer_model)  # Device Name
        worksheet.write(xlsx_row, 1, answers[i][2])  # Device ID
        worksheet.write_url(xlsx_row, 2, f'http://{answers[i][1]}', string=answers[i][1])  # Device IP

        printer_map = {
            "Konica Minolta Bizhub C": ([(2, 3), (3, 3), (4, 4), (5, 4), (6, 4), (7, 5)], 8),
            "Konica Minolta Bizhub": ([(1, 3), (2, 5)], 3),
            "HP LaserJet Pro 200 color M251n": ([(2, 3), (3, 4)], 4),
            "HP Color LaserJet M553dn": ([(2, 3), (3, 4)], 4),
            "HP Color LaserJet MFP E78325": ([(2, 3), (3, 4)], 4),
            "Xerox Versalink B": ([(2, 3), (3, 5)], 4),
            "Xerox WorkCentre 5325": ([(1, 3), (2, 5)], 3),
            "Xerox Versalink C": ([(3, 3), (4, 4), (5, 5)], 6),
            "Ricoh MP2501SP": ([(1, 3), (2, 5), (3, 5)], 4),
            "Ricoh SP4510SF": ([(1, 3), (2, 5), (3, 5)], 4),
        }

        black_pages = color_pages = black_scanned_pages = color_scanned_pages = 0

        for model, (mappings, step) in printer_map.items():
            if printer_model.startswith(model):
                for offset, col in mappings:
                    value = int(answers[i + offset][4])
                    # worksheet.write(xlsx_row, col, value)
                    if col == 3:
                        black_pages += value
                        worksheet.write(xlsx_row, col, black_pages)
                    elif col == 4:
                        color_pages += value
                        worksheet.write(xlsx_row, col, color_pages)
                    elif col == 5:
                        black_scanned_pages += value
                        worksheet.write(xlsx_row, col, black_scanned_pages)
                return xlsx_row + 1, i + step, black_pages, color_pages, black_scanned_pages, color_scanned_pages

        # Alapértelmezett feldolgozás
        black_pages = int(answers[i + 1][4])
        worksheet.write(xlsx_row, 3, black_pages)
        return xlsx_row + 1, i + 2, black_pages, color_pages, black_scanned_pages, color_scanned_pages

    @staticmethod
    def write_bad_answers(worksheet, bad_answers):
        """Kiírja a hibás válaszokat az Excel táblába."""
        xlsx_row = 2
        for answer in bad_answers:
            worksheet.write(xlsx_row, 0, answer[0])  # Device Name
            worksheet.write(xlsx_row, 1, answer[2])  # Device ID
            worksheet.write_url(xlsx_row, 2, f'http://{answer[1]}', string=answer[1])  # Device IP
            xlsx_row += 1
        worksheet.autofit()

    def _add_worksheet_body(self, answers, bad_answers):
        """Az Excel táblázat generálása és SQL parancsok összerakása."""
        print('Exceltábla készítése folyamatban...')
        xlsx_row, i = 2, 0

        while i < len(answers):
            temp_device_id, temp_device_name, temp_device_ip = answers[i][2], answers[i][0], answers[i][1]
            xlsx_row, i, black_pages, color_pages, black_scanned_pages, color_scanned_pages = self.process_printer(
                self.worksheet1, answers, i, xlsx_row
            )

            # SQL törlés és beszúrás
            timestamp = int(time.mktime(self.timestamp.timetuple()))
            self.sql_command += (
                f'DELETE FROM torzsadattar.leltar_nyomtatoallas_rogzites '
                f'WHERE LNyA_LAzonosito = {temp_device_id} AND LNyA_Honap = "{self.year_month}";\n'
                f'INSERT INTO torzsadattar.leltar_nyomtatoallas_rogzites '
                f'(LNyA_LAzonosito, LNyA_Szamlalo_Ff, LNyA_Szamlalo_Co, LNyA_Honap, LNyA_Rogzito, '
                f'LNyA_Datum, LNyA_Megjegyzes, LNyA_Szamlalo_Scan) VALUES ('
                f'{temp_device_id}, {black_pages}, {color_pages}, "{self.year_month}", "2290", {timestamp}, '
                f'"Gépi beolvasás", {black_scanned_pages + color_scanned_pages});\n'
            )

            # Eredménylista frissítése
            self.results.append([
                temp_device_id, temp_device_name, temp_device_ip,
                black_pages, color_pages, black_scanned_pages + color_scanned_pages
            ])

        self.worksheet1.autofit()
        self.worksheet1.autofilter('A2:F2')

        # Hibás válaszok kiírása a második munkalapra
        self.write_bad_answers(self.worksheet2, bad_answers)

        # Excel fájl mentése
        excel_file = f'"{WORK_DIRECTORY}Nyomtatóállások {YEAR_MONTH_DAY_TIME} - {it_specialist}.xlsx"'
        try:
            print('Az EXCEL tábla elkészült!')
        except Exception as e:
            print(f'{excel_file} állomány megnyitása EXCEL programmal nem sikerült! {e}')

    def run(self):

        snmp_printer_getter = SnmpGetter()
        asyncio.run(snmp_printer_getter.run())
        save_printer_oid_answers.SavePrinterOidAnswers(snmp_printer_getter.answers_from_printer,
                                                       snmp_printer_getter.bad_answers_from_printer,
                                                       it_specialist).save_answers()

        self._add_worksheet_body(snmp_printer_getter.answers_from_printer,
                                 snmp_printer_getter.bad_answers_from_printer)
        self.workbook.close()
        self.save_sql()


if __name__ == "__main__":
    argument = []
    for i in range(0, len(sys.argv)):
        argument.append(sys.argv[i].lower())

    if len(sys.argv) < 2 or len(sys.argv) > 2 or (len(sys.argv) == 2 and sys.argv[1].lower() == 'help'):
        print(pyfiglet.figlet_format("Printed Page"))
        print(pyfiglet.figlet_format("SNMP"))
        print(pyfiglet.figlet_format("Reader"))

        print("Using:  SnmpGetPrinterCounter <analyse         - Analyse data from database.>")
        print("                              <erase           - Delete all previously collected data from database.>")
        print("                              <release         - Clear the data from the database and the earlier "
              "scanned device table.>")
        print("                              <run             - Get SNNP data from devices and create excel and SQL.>")
        print("                              <ip              - Get information from new ip table.")
        print("                              <sql             - Write database table data to SQL file.")
        print("                              <help or nothing - current information")

        sys.exit(0)

    argument = sys.argv[1].lower()
    if argument == "sql":
        save_sql_command.run()
    if argument == "analyse":
        analyse_data.run()
    elif argument == 'clear' or argument == 'release':
        conn = database.sql_connect()
        conn.execute('DELETE FROM printers')
        print('Successfully deleted all previously collected data.')
        if argument == 'release':
            with open(f'{WORK_DIRECTORY}scanned_printer.txt', "w", encoding='utf-8') as file:
                file.close()
                print('Successfully deleted all scanned printer data.')
        sys.exit(0)
    elif argument == 'ip':
        NewIpImport().refresh_printers()  # IP list update with new IP addresses
    elif argument == 'run':
        conn = database.sql_connect()
        cursor = conn.cursor
        cursor.execute(
            'CREATE TABLE IF NOT EXISTS printers'
            '(device_id INTEGER,'
            ' timestamp TEXT,'
            ' device_name TEXT,'
            ' device_ip TEXT,'
            ' it_specialist TEXT,'
            ' e_mail TEXT,'
            ' black_pages INTEGER,'
            ' color_pages INTEGER,'
            ' scanned_pages INTEGER)')

        # cursor.execute('DELETE FROM printers')

        it_specialists = set()

        if not os.path.isfile(f'{WORK_DIRECTORY}ip.txt'):
            input('Nem találom az ip.txt állományt a work könyvtárban!')
            sys.exit(0)
        try:
            with open(f'{WORK_DIRECTORY}ip.txt', encoding='utf-8') as file:
                for text_row in file:
                    text_row = text_row.strip().split('\t')
                    if text_row[5] == '1':
                        it_specialists.add(text_row[3])
                    else:
                        print(f'Skipped device ip:{text_row[1]}')

            file.close()

        except Exception as e:
            print(f'Az ip.txt állomány beolvasása nem sikerült! {e}')

        email_addresses = set()

        if not os.path.isfile(f'{WORK_DIRECTORY}email_addresses.txt'):
            input('Nem találom az email_addresses.txt állományt a work könyvtárban!')
            sys.exit(0)
        try:
            with open(f'{WORK_DIRECTORY}email_addresses.txt', encoding='utf-8') as file:
                for text_row in file:
                    text_row = text_row.strip().split('\t')
                    email_addresses.add(text_row[0])
            file.close()

        except Exception as e:
            print(f'Az ip.txt állomány beolvasása nem sikerült! {e}')

        filelist = glob.glob(f'{WORK_DIRECTORY}sql*.txt')
        for filePath in filelist:
            try:
                os.remove(filePath)
            except OSError:
                print("Hiba a file törlése közben!")

        it_specialists = sorted(it_specialists)

        for it_specialist in it_specialists:
            answers = []
            bad_answers = []
            print(f'{it_specialist} informatikus eszközeinek beolvasása elkezdődött...\n')
            ps = PrinterScanner(f'{WORK_DIRECTORY}ip.txt',
                                f'{WORK_DIRECTORY}Nyomtatóállások {YEAR_MONTH_DAY_TIME} - {it_specialist}.xlsx',
                                it_specialist)
            ps.run()

            # Write data to db file.
            for res in ps.results:
                cursor.execute(
                    'INSERT INTO printers (device_id, timestamp, device_name, device_ip, it_specialist, e_mail,'
                    ' black_pages, color_pages, scanned_pages)'
                    'VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
                    (int(res[0]), YEAR_MONTH_DAY_TIME, res[1], res[2], it_specialist, '', res[3], res[4], res[5]))

            # Send e-mail to it_specialist.
            for email_address in email_addresses:
                send_email(it_specialist, email_address, YEAR_MONTH_DAY_TIME, it_specialist)

            del ps
